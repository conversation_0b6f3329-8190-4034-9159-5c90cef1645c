import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>oot<PERSON>,
  TableCell,
  Select,
  SelectItem,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  <PERSON><PERSON>,
  Card,
  Spinner,
} from "@heroui/react";
import {
  Autocomplete,
  AutocompleteItem,
  AutocompleteSection,
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { useQuery, useMutation } from "@apollo/client";

import { ProjectInfo } from "./project-info";
import { HistoryModal } from "./history-modal";
import { EditModal } from "./edit-modal";

import { useProjectUsers } from "@/hooks/users/useProjectUsers";
import { useProjects } from "@/hooks/projects/useProjects";
import { useProductionTeams } from "@/hooks/teams/useProductionTeams";
import { useTemplates } from "@/hooks/templates/useTemplates";
import { getStatusStyle } from "@/components/primitives";
import { Project } from "@/types/projects";
import { ContactsModal } from "@/components/contacts/contacts-modal";
import {
  GET_PROJECT_ALL_DATES,
  UPDATE_PROJECT_USERS,
} from "@/graphql/operations/projects";

interface StatusModalProps {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  onClose: () => void;
  selectedProject: Project | null;
}

export const StatusModal = ({
  isOpen,
  setIsOpen,
  onClose,
  selectedProject,
}: StatusModalProps) => {
  const { loading, error, data, refetch } = useQuery(GET_PROJECT_ALL_DATES, {
    variables: {
      id: selectedProject?.id ? parseInt(selectedProject.id) : null,
    },
    skip: !selectedProject?.id,
  });

  const [updateProjectUsers] = useMutation(UPDATE_PROJECT_USERS);

  // Get implementations from useProjects hook
  const { implementations, fetchImplementationTypes } = useProjects();
  const { projectUsers } = useProjectUsers();

  // Get production teams and templates
  const { productionTeams, fetchProductionTeams } = useProductionTeams();
  const { templates, fetchTemplates } = useTemplates();

  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);
  const [selectedHistory, setSelectedHistory] = useState<number>(-1);
  const [selectedHistoryProject, setSelectedHistoryProject] = useState<
    number | null
  >(null);
  const [status, setStatus] = React.useState<string | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isContactsModalOpen, setIsContactsModalOpen] = useState(false);

  // New state variables for Autocomplete selections (storing user IDs)
  const [coordinatorId, setCoordinatorId] = useState<number | null>(null);
  const [backupId, setBackupId] = useState<number | null>(null);
  const [implementer1Id, setImplementer1Id] = useState<number | null>(null);
  const [implementer2Id, setImplementer2Id] = useState<number | null>(null);
  const [teamId, setTeamId] = useState<number | null>(null);
  const [incubatorId, setIncubatorId] = useState<number | null>(null);
  const [templateId, setTemplateId] = useState<number | null>(null);

  useEffect(() => {
    if (selectedProject) {
      setStatus(selectedProject.estado);
    }
  }, [selectedProject]);

  useEffect(() => {
    if (!projectUsers) return;
  }, [projectUsers]);

  // Fetch implementation types when the modal is opened
  useEffect(() => {
    fetchImplementationTypes();
  }, []);

  // Fetch production teams and templates when the modal is opened
  useEffect(() => {
    if (isOpen) {
      fetchProductionTeams();
      fetchTemplates();
    }

    refetch();
  }, [isOpen]);

  useEffect(() => {
    // Refetch data when the modal is opened or when returning from edit modal
    if (isOpen && selectedProject?.id) {
      refetch({
        id: selectedProject?.id ? parseInt(selectedProject.id) : null,
      });
    }
  }, [isOpen, selectedProject?.id, refetch]);

  const handleSubmit = async () => {
    if (!selectedProject?.id) return;

    try {
      // Create the input object for the mutation
      const input: any = {};

      // Only include fields that have been changed
      if (coordinatorId !== null) input.coordinatorId = coordinatorId;
      if (backupId !== null) input.backupId = backupId;
      if (implementer1Id !== null) input.implementer1Id = implementer1Id;
      if (implementer2Id !== null) input.implementer2Id = implementer2Id;
      if (teamId !== null) input.teamId = teamId;
      if (incubatorId !== null) input.incubatorId = incubatorId;

      // Execute the mutation
      const result = await updateProjectUsers({
        variables: {
          id: parseInt(selectedProject.id),
          input,
        },
      });

      if (result.data?.updateProject?.project) {
        console.log(
          "Project users updated successfully:",
          result.data.updateProject.project,
        );
        // Refetch the project data to update the UI
        refetch();
        onClose();
      }
    } catch (error) {
      console.error("Error updating project users:", error);
    }
  };

  // Create phases structure from project data
  const [projectData, setProjectData] = useState(data?.project || {});

  useEffect(() => {
    if (data?.project) setProjectData(data.project);
  }, [data?.project]);

  const phases = [
    {
      phase: "Start",
      startDate: projectData.startRealInitialDate,
      endDate: projectData.startRealFinalDate,
      estimatedStart: projectData.startInitialDate,
      estimatedEnd: projectData.startFinalDate,
    },
    {
      phase: "Incubadora",
      startDate: projectData.incubadoraRealInitialDate,
      endDate: projectData.incubadoraRealFinalDate,
      estimatedStart: projectData.incubadoraInitialDate,
      estimatedEnd: projectData.incubadoraFinalDate,
    },
    {
      phase: "Migration",
      startDate: projectData.migrationRealInitialDate,
      endDate: projectData.migrationRealFinalDate,
      estimatedStart: projectData.migrationInitialDate,
      estimatedEnd: projectData.migrationFinalDate,
    },
    {
      phase: "Test",
      startDate: projectData.testRealInitialDate,
      endDate: projectData.testRealFinalDate,
      estimatedStart: projectData.testInitialDate,
      estimatedEnd: projectData.testFinalDate,
    },
    {
      phase: "Go Live",
      startDate: projectData.goliveRealInitialDate,
      endDate: projectData.goliveRealFinalDate,
      estimatedStart: projectData.goliveInitialDate,
      estimatedEnd: projectData.goliveFinalDate,
    },
    {
      phase: "Collection",
      startDate: projectData.collectionRealInitialDate,
      endDate: projectData.collectionRealFinalDate,
      estimatedStart: projectData.collectionInitialDate,
      estimatedEnd: projectData.collectionFinalDate,
    },
  ];

  // Loading state component with spinner
  const LoadingComponent = () => (
    <Modal backdrop="opaque" isOpen={isOpen} size="md" onClose={onClose}>
      <ModalContent>
        <Card className="border-none mx-auto p-6 shadow-none w-full">
          <div className="flex flex-col gap-4 items-center justify-center py-8">
            <Spinner color="primary" size="lg" />
            <div className="text-center">
              <h3 className="font-semibold mb-2 text-lg">Cargando</h3>
              <p className="text-center text-default-600">
                Obteniendo información del proyecto...
              </p>
            </div>
          </div>
        </Card>
      </ModalContent>
    </Modal>
  );

  // Error state component with retry button
  const ErrorComponent = () => (
    <Modal backdrop="opaque" isOpen={isOpen} size="md" onClose={onClose}>
      <ModalContent>
        <Card className="border-none mx-auto p-6 shadow-none w-full">
          <div className="flex flex-col gap-4 items-center justify-center py-8">
            <div className="bg-danger-100 dark:bg-danger-900/20 p-3 rounded-full">
              <Icon
                className="text-danger-500"
                icon="lucide:alert-circle"
                width={40}
              />
            </div>
            <div className="text-center">
              <h3 className="font-semibold mb-2 text-lg">Error</h3>
              <p className="mb-4 text-center text-default-600">
                No se pudo cargar la información del proyecto. Por favor intenta
                de nuevo.
              </p>
              <Button
                className="mt-2"
                color="primary"
                startContent={<Icon icon="lucide:refresh-cw" />}
                onPress={() => refetch()}
              >
                Reintentar
              </Button>
            </div>
          </div>
        </Card>
      </ModalContent>
    </Modal>
  );

  if (loading) {
    return <LoadingComponent />;
  }

  if (error) {
    return <ErrorComponent />;
  }

  return (
    <>
      <Modal
        backdrop="opaque"
        classNames={{
          base: "bg-content1",
          header: "border-b border-divider",
          body: "py-6 overflow-visible",
          footer: "border-t border-divider",
          wrapper: " overflow-hidden",
        }}
        isDismissable={false}
        isOpen={isOpen}
        scrollBehavior="inside"
        size="5xl"
        onClose={onClose}
      >
        <ModalContent>
          {() => (
            <>
              <ModalHeader className="w-full flex items-center justify-center">
                <div
                  className="flex justify-between items-center"
                  style={{ width: "95%" }}
                >
                  <ProjectInfo project={data?.project || selectedProject} />
                </div>
              </ModalHeader>
              <ModalBody className="overflow-y-auto">
                <>
                  <>
                    <div className="w-full">
                      {/* First row - 3 autocomplete components */}
                      <div className="grid grid-cols-4 gap-4 mb-4">
                        <Autocomplete
                          className="w-full"
                          defaultSelectedKey={
                            projectData.coordinator?.name || ""
                          }
                          label="Coordinador"
                          labelPlacement="outside"
                          placeholder="Seleccionar coordinador"
                          radius="sm"
                          shouldCloseOnBlur={false}
                          variant="bordered"
                          onSelectionChange={(key) => {
                            if (key === null) {
                              setCoordinatorId(null);
                            } else {
                              const selectedUser = projectUsers.find(
                                (user) => user.name === key,
                              );

                              setCoordinatorId(selectedUser?.user_id || null);
                            }
                          }}
                        >
                          {Object.entries(
                            projectUsers.reduce(
                              (groups, user) => {
                                const groupName =
                                  user.group_name || "Sin grupo";

                                if (!groups[groupName]) {
                                  groups[groupName] = [];
                                }
                                groups[groupName].push(user);

                                return groups;
                              },
                              {} as Record<string, typeof projectUsers>,
                            ),
                          ).map(([groupName, users]) => (
                            <AutocompleteSection
                              key={groupName}
                              showDivider
                              title={groupName}
                            >
                              {users.map((user) => (
                                <AutocompleteItem key={user.name}>
                                  {user.name}
                                </AutocompleteItem>
                              ))}
                            </AutocompleteSection>
                          ))}
                        </Autocomplete>

                        <Autocomplete
                          className="w-full"
                          defaultSelectedKey={
                            projectData.implementer1?.name || ""
                          }
                          label="Implementador 1"
                          labelPlacement="outside"
                          placeholder="Seleccionar implementador"
                          radius="sm"
                          shouldCloseOnBlur={false}
                          variant="bordered"
                          onSelectionChange={(key) => {
                            if (key === null) {
                              setImplementer1Id(null);
                            } else {
                              const selectedUser = projectUsers.find(
                                (user) => user.name === key,
                              );

                              setImplementer1Id(selectedUser?.user_id || null);
                            }
                          }}
                        >
                          {Object.entries(
                            projectUsers.reduce(
                              (groups, user) => {
                                const groupName =
                                  user.group_name || "Sin grupo";

                                if (!groups[groupName]) {
                                  groups[groupName] = [];
                                }
                                groups[groupName].push(user);

                                return groups;
                              },
                              {} as Record<string, typeof projectUsers>,
                            ),
                          ).map(([groupName, users]) => (
                            <AutocompleteSection
                              key={groupName}
                              showDivider
                              title={groupName}
                            >
                              {users.map((user) => (
                                <AutocompleteItem key={user.name}>
                                  {user.name}
                                </AutocompleteItem>
                              ))}
                            </AutocompleteSection>
                          ))}
                        </Autocomplete>
                        <Autocomplete
                          className="w-full"
                          defaultSelectedKey={
                            projectData.implementer2?.name || ""
                          }
                          label="Implementador 2"
                          labelPlacement="outside"
                          placeholder="Seleccionar implementador"
                          radius="sm"
                          shouldCloseOnBlur={false}
                          variant="bordered"
                          onSelectionChange={(key) => {
                            if (key === null) {
                              setImplementer2Id(null);
                            } else {
                              const selectedUser = projectUsers.find(
                                (user) => user.name === key,
                              );

                              setImplementer2Id(selectedUser?.user_id || null);
                            }
                          }}
                        >
                          {Object.entries(
                            projectUsers.reduce(
                              (groups, user) => {
                                const groupName =
                                  user.group_name || "Sin grupo";

                                if (!groups[groupName]) {
                                  groups[groupName] = [];
                                }
                                groups[groupName].push(user);

                                return groups;
                              },
                              {} as Record<string, typeof projectUsers>,
                            ),
                          ).map(([groupName, users]) => (
                            <AutocompleteSection
                              key={groupName}
                              showDivider
                              title={groupName}
                            >
                              {users.map((user) => (
                                <AutocompleteItem key={user.name}>
                                  {user.name}
                                </AutocompleteItem>
                              ))}
                            </AutocompleteSection>
                          ))}
                        </Autocomplete>

                        <Autocomplete
                          className="w-full"
                          label="Plantilla"
                          labelPlacement="outside"
                          placeholder="Seleccionar plantilla"
                          radius="sm"
                          shouldCloseOnBlur={false}
                          variant="bordered"
                          onSelectionChange={(key) => {
                            if (key === null) {
                              setTemplateId(null);
                            } else {
                              const selectedTemplate = templates.find(
                                (template) => template.name === key,
                              );

                              setTemplateId(selectedTemplate?.id || null);
                            }
                          }}
                        >
                          {templates.map((template) => (
                            <AutocompleteItem key={template.name}>
                              {template.name}
                            </AutocompleteItem>
                          ))}
                        </Autocomplete>
                      </div>

                      {/* Second row - 5 autocomplete/select components */}
                      <div className="grid grid-cols-4 gap-4">
                        <Autocomplete
                          className="w-full"
                          defaultSelectedKey={projectData.incubator?.name || ""}
                          label="Incubador"
                          labelPlacement="outside"
                          placeholder="Seleccionar Backup"
                          radius="sm"
                          shouldCloseOnBlur={false}
                          variant="bordered"
                          onSelectionChange={(key) => {
                            if (key === null) {
                              setIncubatorId(null);
                            } else {
                              const selectedUser = projectUsers.find(
                                (user) => user.name === key,
                              );

                              setIncubatorId(selectedUser?.user_id || null);
                            }
                          }}
                        >
                          {Object.entries(
                            projectUsers.reduce(
                              (groups, user) => {
                                const groupName =
                                  user.group_name || "Sin grupo";

                                if (!groups[groupName]) {
                                  groups[groupName] = [];
                                }
                                groups[groupName].push(user);

                                return groups;
                              },
                              {} as Record<string, typeof projectUsers>,
                            ),
                          ).map(([groupName, users]) => (
                            <AutocompleteSection
                              key={groupName}
                              showDivider
                              title={groupName}
                            >
                              {users.map((user) => (
                                <AutocompleteItem key={user.name}>
                                  {user.name}
                                </AutocompleteItem>
                              ))}
                            </AutocompleteSection>
                          ))}
                        </Autocomplete>

                        <Autocomplete
                          className="w-full"
                          defaultSelectedKey={projectData.backup?.name || ""}
                          label="Backup"
                          labelPlacement="outside"
                          placeholder="Seleccionar Backup"
                          radius="sm"
                          shouldCloseOnBlur={false}
                          variant="bordered"
                          onSelectionChange={(key) => {
                            if (key === null) {
                              setBackupId(null);
                            } else {
                              const selectedUser = projectUsers.find(
                                (user) => user.name === key,
                              );

                              setBackupId(selectedUser?.user_id || null);
                            }
                          }}
                        >
                          {Object.entries(
                            projectUsers.reduce(
                              (groups, user) => {
                                const groupName =
                                  user.group_name || "Sin grupo";

                                if (!groups[groupName]) {
                                  groups[groupName] = [];
                                }
                                groups[groupName].push(user);

                                return groups;
                              },
                              {} as Record<string, typeof projectUsers>,
                            ),
                          ).map(([groupName, users]) => (
                            <AutocompleteSection
                              key={groupName}
                              showDivider
                              title={groupName}
                            >
                              {users.map((user) => (
                                <AutocompleteItem key={user.name}>
                                  {user.name}
                                </AutocompleteItem>
                              ))}
                            </AutocompleteSection>
                          ))}
                        </Autocomplete>

                        <Autocomplete
                          className="w-full"
                          defaultSelectedKey={projectData.team?.name || ""}
                          label="Equipo"
                          labelPlacement="outside"
                          placeholder="Seleccionar equipo"
                          radius="sm"
                          shouldCloseOnBlur={false}
                          variant="bordered"
                          onSelectionChange={(key) => {
                            if (key === null) {
                              setTeamId(null);
                            } else {
                              const selectedTeam = productionTeams.find(
                                (team) => team.name === key,
                              );

                              setTeamId(selectedTeam?.id || null);
                            }
                          }}
                        >
                          {productionTeams.map((team) => (
                            <AutocompleteItem key={team.name}>
                              {team.name}
                            </AutocompleteItem>
                          ))}
                        </Autocomplete>

                        <Select
                          key={selectedProject?.estado}
                          classNames={{
                            value: "font-medium",
                          }}
                          label="Estado"
                          labelPlacement="outside"
                          placeholder="Seleccionar estado"
                          radius="sm"
                          selectedKeys={status ? [status] : undefined}
                          startContent={
                            status ? (
                              status === "En curso" ? (
                                <Icon
                                  className="text-success-500"
                                  icon="lucide:play-circle"
                                  width={16}
                                />
                              ) : status === "Prevista" ? (
                                <Icon
                                  className="text-warning-500"
                                  icon="lucide:clock"
                                  width={16}
                                />
                              ) : status === "On hold" ? (
                                <Icon
                                  className="text-warning-500"
                                  icon="lucide:pause-circle"
                                  width={16}
                                />
                              ) : status === "Completado" ? (
                                <Icon
                                  className="text-success-500"
                                  icon="lucide:check-circle"
                                  width={16}
                                />
                              ) : (
                                <Icon
                                  className="text-danger-500"
                                  icon="lucide:x-circle"
                                  width={16}
                                />
                              )
                            ) : (
                              <Icon
                                className="text-default-500"
                                icon="lucide:circle"
                                width={16}
                              />
                            )
                          }
                          onChange={(e) => setStatus(e.target.value)}
                        >
                          {["En curso", "Prevista", "On hold", "Cancelado"].map(
                            (state) => (
                              <SelectItem key={state}>{state}</SelectItem>
                            ),
                          )}
                        </Select>
                      </div>
                    </div>
                    <div className="overflow-x-auto">
                      <Card className="shadow-none border border-divider">
                        <Table
                          removeWrapper
                          aria-label="Project dates"
                          classNames={{
                            th: "bg-default-50 font-medium text-default-700 text-sm",
                            td: "py-3 text-sm",
                            table: "min-w-full",
                          }}
                        >
                          <TableHeader>
                            <TableColumn width={120}>Fases</TableColumn>
                            <TableColumn>Fecha inicio prevista</TableColumn>
                            <TableColumn>Fecha inicio real</TableColumn>
                            <TableColumn>Fecha fin prevista</TableColumn>
                            <TableColumn>Fecha fin real</TableColumn>
                            <TableColumn>Mes test 1</TableColumn>
                            <TableColumn>Mes test 2</TableColumn>
                            <TableColumn className="text-center" width={90}>
                              Acciones
                            </TableColumn>
                          </TableHeader>
                          <TableBody>
                            {phases.map((phase, index) => (
                              <TableRow key={index}>
                                <TableCell
                                  className="font-medium"
                                  style={{
                                    backgroundColor: getStatusStyle(
                                      phase.phase,
                                    ),
                                  }}
                                >
                                  {phase.phase}
                                </TableCell>
                                <TableCell>
                                  {phase.estimatedStart || "-"}
                                </TableCell>
                                <TableCell>{phase.startDate || "-"}</TableCell>
                                <TableCell>
                                  {phase.estimatedEnd || "-"}
                                </TableCell>
                                <TableCell>{phase.endDate || "-"}</TableCell>
                                <TableCell className="">
                                  {phase.phase === "Test"
                                    ? projectData.month1Test || "-"
                                    : "-"}
                                </TableCell>
                                <TableCell className="">
                                  {phase.phase === "Test"
                                    ? projectData.month2Test || "-"
                                    : "-"}
                                </TableCell>
                                <TableCell className="text-center flex justify-center items-center gap-2 min-h-[40px]">
                                  <Button
                                    isIconOnly
                                    color="primary"
                                    size="sm"
                                    variant="flat"
                                    onPress={() => {
                                      setSelectedHistory(index);
                                      setIsEditModalOpen(true);
                                    }}
                                  >
                                    <Icon icon="lucide:edit-3" width={18} />
                                  </Button>
                                  <Button
                                    isIconOnly
                                    color="secondary"
                                    size="sm"
                                    variant="flat"
                                    onPress={() => {
                                      setSelectedHistoryProject(
                                        selectedProject?.id
                                          ? parseInt(selectedProject.id)
                                          : null,
                                      );
                                      setIsHistoryModalOpen(true);
                                    }}
                                  >
                                    <Icon icon="lucide:history" width={18} />
                                  </Button>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </Card>
                    </div>
                  </>
                </>
              </ModalBody>
              <ModalFooter className="w-full flex justify-between">
                <div>
                  <Button
                    isIconOnly
                    aria-label="Contacts"
                    className="text-white"
                    color="primary"
                    onPress={() => {
                      setIsContactsModalOpen(true);
                      onClose();
                    }}
                  >
                    <Icon icon="lucide:users" width={20} />
                  </Button>
                </div>
                <div>
                  <Button
                    color="danger"
                    radius="sm"
                    variant="light"
                    onPress={onClose}
                  >
                    Cancelar
                  </Button>
                  <Button
                    color="primary"
                    radius="sm"
                    type="submit"
                    onPress={handleSubmit}
                  >
                    Guardar
                  </Button>
                </div>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>

      <HistoryModal
        isOpen={isHistoryModalOpen}
        selectedProject={selectedHistoryProject}
        onClose={() => {
          console.log("History modal closed");
          setIsHistoryModalOpen(false);
          // setIsOpen(true);
        }}
      />

      <EditModal
        isOpen={isEditModalOpen}
        phase={selectedHistory >= 0 ? phases[selectedHistory].phase : ""}
        selectedHistory={selectedHistory}
        selectedProject={selectedProject}
        onClose={() => {
          setIsEditModalOpen(false);
          setIsOpen(true);
        }}
      />

      <ContactsModal
        isOpen={isContactsModalOpen}
        selectedProject={selectedHistoryProject}
        onClose={() => {
          setIsContactsModalOpen(false);
          setIsOpen(true);
        }}
      />
    </>
  );
};
