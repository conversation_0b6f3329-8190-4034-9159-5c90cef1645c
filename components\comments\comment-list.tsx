import React from "react";
import { Avatar } from "@heroui/react";
import { Icon } from "@iconify/react";

import { Comment } from "@/types/comment";
import { formatTimeAgo } from "@/utils/date-formatter";

interface CommentListProps {
  comments: Comment[];
}

export const CommentList: React.FC<CommentListProps> = ({ comments }) => {
  if (comments.length === 0) {
    return (
      <div className="text-center py-8 text-default-500">
        <Icon className="mx-auto mb-2 text-3xl" icon="lucide:message-square" />
        <p>No comments yet. Be the first to comment!</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {comments.map((comment) => (
        <div key={comment.id} className="flex gap-4">
          <Avatar
            className="flex-shrink-0"
            name={comment.author}
            size="md"
            src={comment.avatarUrl}
          />
          <div className="flex-grow">
            <div className="flex items-center gap-2 mb-1">
              <h3 className="font-semibold">{comment.author}</h3>
              <span className="text-default-400 text-xs">
                {/* {formatTimeAgo(new Date(comment.timestamp))} */}
                {comment.timestamp}
              </span>
            </div>
            <p className="text-default-700 mb-3">{comment.content}</p>
          </div>
        </div>
      ))}
    </div>
  );
};
