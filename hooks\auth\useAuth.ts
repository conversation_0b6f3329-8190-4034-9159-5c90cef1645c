// hooks/useAuth.ts
"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";

import { useRoles } from "../users/useRoles";

import { axiosInstance } from "@/lib/axios";
import { API_ROUTES } from "@/lib/api";
import { useUserStore } from "@/store/use-user-store";

const DISABLE_AUTH = process.env.NEXT_PUBLIC_DISABLE_AUTH === "true";

export function useAuth() {
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const { setUserInfo, userInfo } = useUserStore();
  const { roles, fetchRoles } = useRoles();

  useEffect(() => {
    if (DISABLE_AUTH) {
      setLoading(false);

      return;
    }

    const accessToken = localStorage.getItem("accessToken");

    if (accessToken) {
      fetchUser();
    } else {
      setLoading(false);
    }
  }, []);

  const fetchUser = async () => {
    try {
      setLoading(true);
      const res = await axiosInstance.get(API_ROUTES.USER_PROFILE);

      setUserInfo(res.data);
      setUser(res.data);
    } catch (error) {
      console.error("Fetch user error:", error);
      // Try to refresh the token if fetch user fails
      const refreshResult = await refreshAccessToken();

      // If refresh was successful, try fetching user again
      if (refreshResult) {
        try {
          const res = await axiosInstance.get(API_ROUTES.USER_PROFILE);

          setUser(res.data);
          setUserInfo(res.data);
        } catch (err) {
          console.error("Fetch user after refresh error:", err);
          logout();
        }
      }
    } finally {
      setLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    if (DISABLE_AUTH) return;

    try {
      const res = await axiosInstance.post(API_ROUTES.LOGIN, {
        email,
        password,
      });

      if (res.status !== 200) {
        throw new Error("Invalid credentials");
      }

      // Store tokens in localStorage
      localStorage.setItem("accessToken", res.data.access);
      localStorage.setItem("refreshToken", res.data.refresh);

      if (res.data.must_change_password) {
        console.log("User must change password");
        const resetPasswordToken = res.data.password_reset_token;

        router.push(`/login/reiniciar?token=${resetPasswordToken}`);

        return false;
      }

      // Update axios instance with the new token
      axiosInstance.defaults.headers.common["Authorization"] =
        `Bearer ${res.data.access}`;

      await fetchUser();

      return true;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data?.detail || "Invalid credentials");
      } else {
        throw error;
      }
    }
  };

  const logout = () => {
    if (DISABLE_AUTH) return;

    localStorage.removeItem("accessToken");
    localStorage.removeItem("refreshToken");
    delete axiosInstance.defaults.headers.common["Authorization"];
    setUser(null);
    router.push("/login");
  };

  const refreshAccessToken = async (): Promise<boolean> => {
    if (DISABLE_AUTH) {
      return true;
    }

    const refreshToken = localStorage.getItem("refreshToken");

    if (!refreshToken) {
      logout();

      return false;
    }

    try {
      const res = await axiosInstance.post(API_ROUTES.REFRESH, {
        refresh: refreshToken,
      });

      localStorage.setItem("accessToken", res.data.access);
      axiosInstance.defaults.headers.common["Authorization"] =
        `Bearer ${res.data.access}`;

      return true;
    } catch (error) {
      logout();

      return false;
    }
  };

  const isAuthenticated = async () => {
    if (DISABLE_AUTH) return true;

    const accessToken = localStorage.getItem("accessToken");
    const refreshToken = localStorage.getItem("refreshToken");

    if (!accessToken && !refreshToken) {
      logout();
    }
    if (accessToken) {
      const tokenParts = JSON.parse(atob(accessToken.split(".")[1]));
      const now = Math.floor(Date.now() / 1000);

      if (tokenParts.exp < now) {
        // Access token is expired, try to refresh it
        const refreshResult = await refreshAccessToken();

        return refreshResult;
      }

      return true;
    }
    if (refreshToken) {
      const tokenParts = JSON.parse(atob(refreshToken.split(".")[1]));
      const now = Math.floor(Date.now() / 1000);

      if (tokenParts.exp < now) {
        logout();

        return false;
      }

      return true;
    }

    return false;
  };

  const resetPassword = async (newPassword: string) => {
    if (DISABLE_AUTH) return;
    try {
      const res = await axiosInstance.post(API_ROUTES.RESET_PASSWORD, {
        new_password: newPassword,
      });

      if (res.status !== 200) {
        throw new Error("Error al cambiar la contraseña");
      }

      return true;
    } catch (error: any) {
      if (error.response) {
        throw new Error(
          error.response.data?.detail || "Error al cambiar la contraseña",
        );
      } else {
        throw error;
      }
    }
  };

  // Check if user has specific permission
  const hasPermission = (permission: string): boolean => {
    console.log(`Checking permission: ${permission}`);

    if (DISABLE_AUTH) {
      console.log("Auth disabled, returning true automatically");

      return true;
    }

    console.log("User info:", userInfo);
    if (!userInfo) {
      console.log("No user info available, returning false");

      return false;
    }

    // Find user's role from groups or set a default
    console.log("User groups:", userInfo.groups);
    const userRole =
      userInfo.groups && userInfo.groups.length > 0
        ? userInfo.groups[0] // Using first group as role
        : null;

    console.log("Selected user role:", userRole);

    // Check if any permission object has the matching codename
    const hasPermission = userRole.permissions.some(
      (p: any) => p.codename.toLowerCase() === permission.toLowerCase(),
    );

    console.log(`Permission check result: ${hasPermission}`);

    return hasPermission;
  };

  // Check if user has a specific role
  const hasRole = (roleName: string): boolean => {
    if (DISABLE_AUTH) return true;
    if (!userInfo) return false;

    // Find user's role from groups or set a default
    const userRole =
      userInfo.groups && userInfo.groups.length > 0
        ? userInfo.groups[0] // Using first group as role
        : null;

    return userRole
      ? userRole.name.toLowerCase() === roleName.toLowerCase()
      : false;
  };

  return {
    user,
    login,
    logout,
    refreshAccessToken,
    fetchUser,
    loading,
    isAuthenticated,
    hasPermission,
    hasRole,
    resetPassword,
  };
}
