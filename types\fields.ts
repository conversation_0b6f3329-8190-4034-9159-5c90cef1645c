export interface Field {
  id: number;
  name: string;
  description: string;
  field_type: "Informativo" | "Selección" | "Tarea" | "Documento" | "Subtarea";
  phase:
    | "Start"
    | "Collection"
    | "Migration"
    | "Go Live"
    | "Test"
    | "Incubadora";
  subphase: string;
  hito: string;
  weight: number;

  value?: string;
}

export interface FieldProps {
  id: number;
  title: string;
  description: string;
  type: "Informativo" | "Tarea" | "Selección" | "Documento" | "Subtarea";
  observation: string;
  milestone: boolean;
  options?: { key: string; label: string }[];
  edit?: boolean;
  subtasks?: {
    id: number;
    title: string;
    description: string;
    observation: string;
    value: string;
  }[];
  value?: string;
}
