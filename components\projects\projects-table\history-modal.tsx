"use client";
import type { SortDescriptor } from "@heroui/system-rsc/node_modules/@react-types/shared";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  Modal<PERSON>ooter,
  TableCell,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  Spinner,
} from "@nextui-org/react";
import { Button } from "@heroui/button";
import { useState, useEffect } from "react";

import { getPhaseStyleText } from "@/components/primitives";
import {
  DateChangeLogs,
  useProjectDates,
} from "@/hooks/projects/useProjectDates";

interface HistoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedProject: number | null;
}

export const HistoryModal = ({
  isOpen,
  onClose,
  selectedProject,
}: HistoryModalProps) => {
  const { dateChangeLogs, fetchDateChangeLogs, loading } = useProjectDates();

  const [sortDescriptor, setSortDescriptor] = useState<SortDescriptor>({
    column: "timestamp" as string,
    direction: "descending",
  });

  // Fetch data when modal opens and selectedProject is available
  useEffect(() => {
    if (isOpen && selectedProject) {
      fetchDateChangeLogs(selectedProject);
    }
  }, [isOpen, selectedProject]);

  const sortedItems = [...dateChangeLogs].sort((a, b) => {
    const first = a[sortDescriptor.column as keyof DateChangeLogs] as string;
    const second = b[sortDescriptor.column as keyof DateChangeLogs] as string;
    const cmp = first.localeCompare(second);

    return sortDescriptor.direction === "descending" ? -cmp : cmp;
  });

  const handleSortChange = (descriptor: SortDescriptor) => {
    setSortDescriptor(descriptor);
  };

  return (
    <>
      <Modal
        isDismissable={true}
        isKeyboardDismissDisabled={true}
        isOpen={isOpen}
        size="4xl"
        style={{ zIndex: 1000 }}
        onClose={onClose}
      >
        <ModalContent>
          {() => (
            <>
              <ModalHeader>Historial</ModalHeader>
              <ModalBody>
                {loading ? (
                  <div className="flex justify-center items-center py-8">
                    <Spinner size="lg" />
                  </div>
                ) : (
                  <Table
                    removeWrapper
                    sortDescriptor={sortDescriptor}
                    onSortChange={handleSortChange}
                  >
                    <TableHeader>
                      <TableColumn key="timestamp" allowsSorting>
                        Timestamp
                      </TableColumn>
                      <TableColumn key="phase" allowsSorting>
                        Fase
                      </TableColumn>
                      <TableColumn key="field_type" allowsSorting>
                        Tipo
                      </TableColumn>
                      <TableColumn key="new_value" allowsSorting>
                        Valor
                      </TableColumn>
                      <TableColumn key="user" allowsSorting>
                        Usuario
                      </TableColumn>
                      <TableColumn key="comment" allowsSorting>
                        Comentario
                      </TableColumn>
                    </TableHeader>
                    <TableBody
                      emptyContent="No hay datos de historial disponibles"
                      items={sortedItems}
                    >
                      {(item) => (
                        <TableRow key={item.id}>
                          <TableCell>
                            {new Date(item.timestamp).toLocaleDateString()}
                          </TableCell>
                          <TableCell className={getPhaseStyleText(item.phase)}>
                            {item.phase}
                          </TableCell>
                          <TableCell>
                            {item.field_type
                              .replaceAll("_", " ")
                              .replace(/\b\w/g, (c) => c.toUpperCase())}
                          </TableCell>
                          <TableCell>
                            {new Date(item.new_value).toLocaleDateString()}
                          </TableCell>
                          <TableCell>{item.user}</TableCell>
                          <TableCell>{item.comment}</TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                )}
              </ModalBody>
              <ModalFooter className="flex justify-between">
                <Button color="danger" variant="light" onPress={onClose}>
                  Cerrar
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
};
