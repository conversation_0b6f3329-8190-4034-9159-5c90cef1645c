"use client";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from "@heroui/react";

interface Contact {
  id: number;
  type: string;
  name: string;
  email: string;
  position: string;
  project: string;
}

interface DeleteContactModalProps {
  onClose: () => void;
  onConfirm: () => void;
  contact: Contact;
  isLoading?: boolean;
}

const DeleteContactModal = ({
  onClose,
  onConfirm,
  contact,
  isLoading = false,
}: DeleteContactModalProps) => {
  return (
    <>
      <ModalHeader>
        <h3 className="text-xl font-bold">Confirmar Eliminación</h3>
      </ModalHeader>
      <ModalBody>
        <p>
          ¿Está seguro de que desea eliminar el contacto{" "}
          <span className="font-semibold">{contact.name}</span>?
        </p>
        <div className="mt-2 text-sm text-gray-600">
          <p><strong>Tipo:</strong> {contact.type}</p>
          <p><strong>Email:</strong> {contact.email}</p>
          <p><strong>Cargo:</strong> {contact.position}</p>
          <p><strong>Implementación:</strong> {contact.project}</p>
        </div>
        <p className="mt-3 text-sm text-gray-500">
          Esta acción no se puede deshacer.
        </p>
      </ModalBody>
      <ModalFooter>
        <Button
          color="default"
          variant="flat"
          onPress={onClose}
        >
          Cancelar
        </Button>
        <Button 
          color="danger" 
          isLoading={isLoading}
          onPress={onConfirm}
        >
          {isLoading ? "Eliminando..." : "Eliminar Contacto"}
        </Button>
      </ModalFooter>
    </>
  );
};

export default DeleteContactModal;
