import { Avatar } from "@nextui-org/react";

import { getGradientFromName } from "./utils";

export const UserAvatar = (name: string, theme: string | undefined) => {
  theme = theme || "light";
  const gradient = getGradientFromName(name, theme === "dark");

  // Get initials from name
  const getInitials = (name: string) => {
    if (!name || name.length === 0) return "NNN.";

    return (
      name
        .split(" ")
        .map((part) => part.charAt(0))
        .join(".")
        .toUpperCase()
        .substring(0, 3) + "."
    );
  };

  return (
    <Avatar
      showFallback
      classNames={{
        icon: "text-black/80",
      }}
      fallback={getInitials(name)}
      name={name}
      style={{
        background: `${gradient.from}`,
      }}
    />
  );
};
