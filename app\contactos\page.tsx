"use client";

import { useState, useEffect } from "react";
import {
  Button,
  Input,
  Card,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Modal,
  Modal<PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  Spinner,
} from "@heroui/react";
import { Icon } from "@iconify/react";

import { title } from "@/components/primitives";
import { useAuth } from "@/hooks/auth/useAuth";
import CreateContactForm from "@/components/contacts/create-contact-form";
import EditContactForm from "@/components/contacts/edit-contact-form";
import DeleteContactModal from "@/components/contacts/delete-contact-modal";
import { FilterDropdown } from "@/components/projects/projects-table/filter-dropdown";
import { useRowCountStore } from "@/store/use-row-count-store";

interface Contact {
  id: number;
  type: string;
  name: string;
  email: string;
  position: string;
  project: string;
}

// Extended dummy data for the contacts view
const dummyContacts: Contact[] = [
  {
    id: 1,
    type: "Empresa",
    name: "<PERSON>",
    email: "<EMAIL>",
    position: "Director",
    project: "Proyecto A",
  },
  {
    id: 2,
    type: "Agregador",
    name: "<PERSON>",
    email: "<EMAIL>",
    position: "Gerente de Ventas",
    project: "Proyecto B",
  },
  {
    id: 3,
    type: "Add-on",
    name: "Luis Martínez",
    email: "<EMAIL>",
    position: "Especialista",
    project: "Proyecto C",
  },
  {
    id: 4,
    type: "OOS",
    name: "María Fernández",
    email: "<EMAIL>",
    position: "Consultora",
    project: "Proyecto A",
  },
  {
    id: 5,
    type: "Impuestos",
    name: "Jorge Ramírez",
    email: "<EMAIL>",
    position: "Contador",
    project: "Proyecto A",
  },
  {
    id: 6,
    type: "SS",
    name: "Elena Torres",
    email: "<EMAIL>",
    position: "Analista",
    project: "Proyecto B",
  },
  {
    id: 7,
    type: "GL File",
    name: "Pedro Sánchez",
    email: "<EMAIL>",
    position: "Administrador",
    project: "Proyecto C",
  },
];

export default function ContactosPage() {
  const { rowCount } = useRowCountStore();

  const [contacts, setContacts] = useState<Contact[]>(dummyContacts);
  const [filteredContacts, setFilteredContacts] =
    useState<Contact[]>(dummyContacts);
  const [searchTerm, setSearchTerm] = useState("");
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [page, setPage] = useState(1);

  // Filter and sort states
  const [activeFilters, setActiveFilters] = useState<Record<string, string[]>>(
    {},
  );
  const [sortConfig, setSortConfig] = useState<{
    column: string;
    direction: "asc" | "desc";
  } | null>(null);

  // Check if user has permission to edit configuration
  const { hasPermission } = useAuth();
  const [canEditConfiguration, setCanEditConfiguration] = useState(false);

  useEffect(() => {
    setCanEditConfiguration(hasPermission("editar_config"));
  }, [hasPermission]);

  // Calculate pagination
  const pages = Math.ceil(filteredContacts.length / rowCount);
  const items = filteredContacts.slice((page - 1) * rowCount, page * rowCount);

  // Function to get unique values for filter dropdown
  const getUniqueValues = (column: keyof Contact): string[] => {
    const uniqueValues = Array.from(
      new Set(
        contacts.map((contact) => {
          const value = contact[column];

          return typeof value === "string" ? value : String(value);
        }),
      ),
    );

    return uniqueValues;
  };

  // Apply filters and search
  const applyFiltersAndSort = (
    searchValue: string,
    filters: Record<string, string[]>,
    sort: { column: string; direction: "asc" | "desc" } | null,
  ) => {
    let filtered = [...contacts];

    // Apply search
    if (searchValue) {
      filtered = filtered.filter(
        (contact) =>
          contact.name.toLowerCase().includes(searchValue.toLowerCase()) ||
          contact.email.toLowerCase().includes(searchValue.toLowerCase()) ||
          contact.type.toLowerCase().includes(searchValue.toLowerCase()) ||
          contact.position.toLowerCase().includes(searchValue.toLowerCase()),
      );
    }

    // Apply filters
    Object.entries(filters).forEach(([column, values]) => {
      if (values.length > 0) {
        filtered = filtered.filter((contact) =>
          values.includes(String(contact[column as keyof Contact])),
        );
      }
    });

    // Apply sorting
    if (sort) {
      filtered.sort((a, b) => {
        const aValue = String(a[sort.column as keyof Contact]);
        const bValue = String(b[sort.column as keyof Contact]);

        if (sort.direction === "asc") {
          return aValue.localeCompare(bValue);
        } else {
          return bValue.localeCompare(aValue);
        }
      });
    }

    setFilteredContacts(filtered);
    setPage(1); // Reset to first page when filtering
  };

  // Handle filter changes
  const handleFilterChange = (column: string, values: string[]): void => {
    const newFilters = { ...activeFilters };

    if (values.length === 0) {
      delete newFilters[column];
    } else {
      newFilters[column] = values;
    }

    setActiveFilters(newFilters);
    applyFiltersAndSort(searchTerm, newFilters, sortConfig);
  };

  // Handle sorting
  const handleSort = (column: string, direction: "asc" | "desc") => {
    const newSortConfig = { column, direction };

    setSortConfig(newSortConfig);
    applyFiltersAndSort(searchTerm, activeFilters, newSortConfig);
  };

  // Handle search
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    applyFiltersAndSort(value, activeFilters, sortConfig);
  };

  // Handle clear filters
  const handleClearFilters = () => {
    setSearchTerm("");
    setActiveFilters({});
    setSortConfig(null);
    setFilteredContacts(contacts);
    setPage(1);
  };

  // Handle add contact
  const handleAddContact = (newContact: Omit<Contact, "id">) => {
    const contactWithId = {
      ...newContact,
      id: Math.max(...contacts.map((c) => c.id)) + 1,
    };
    const updatedContacts = [...contacts, contactWithId];

    setContacts(updatedContacts);
    applyFiltersAndSort(searchTerm, activeFilters, sortConfig);
  };

  // Handle edit contact
  const handleEditContact = (contact: Contact) => {
    console.log("Edit contact clicked:", contact);
    console.log("Can edit configuration:", canEditConfiguration);
    setSelectedContact(contact);
    setIsEditModalOpen(true);
  };

  // Handle update contact
  const handleUpdateContact = (updatedContact: Contact) => {
    const updatedContacts = contacts.map((contact) =>
      contact.id === updatedContact.id ? updatedContact : contact,
    );

    setContacts(updatedContacts);
    applyFiltersAndSort(searchTerm, activeFilters, sortConfig);
    setIsEditModalOpen(false);
    setSelectedContact(null);
  };

  // Handle delete contact
  const handleDeleteContact = (contact: Contact) => {
    console.log("Delete contact clicked:", contact);
    console.log("Can edit configuration:", canEditConfiguration);
    setSelectedContact(contact);
    setIsDeleteModalOpen(true);
  };

  // Handle confirm delete
  const handleConfirmDelete = () => {
    if (!selectedContact) return;

    setIsLoading(true);
    // Simulate API call delay
    setTimeout(() => {
      const updatedContacts = contacts.filter(
        (contact) => contact.id !== selectedContact.id,
      );

      setContacts(updatedContacts);
      applyFiltersAndSort(searchTerm, activeFilters, sortConfig);
      setIsLoading(false);
      setIsDeleteModalOpen(false);
      setSelectedContact(null);
    }, 500);
  };

  return (
    <>
      {/* Debug info */}
      {/* <div className="mb-4 p-2 bg-gray-100 text-xs">
        <p>Edit Modal Open: {isEditModalOpen.toString()}</p>
        <p>Delete Modal Open: {isDeleteModalOpen.toString()}</p>
        <p>Selected Contact: {selectedContact ? selectedContact.name : 'None'}</p>
        <p>Can Edit Configuration: {canEditConfiguration.toString()}</p>
      </div> */}

      <div className="flex justify-between items-center w-full mb-6">
        <h2 className={title({ size: "sm" })}>Contactos</h2>
        <Button
          color="primary"
          disabled={!canEditConfiguration}
          startContent={<Icon icon="heroicons:plus" />}
          onPress={() => setIsCreateModalOpen(true)}
        >
          Crear Contacto
        </Button>
      </div>

      {/* Search and filters */}
      <Card className="p-2 mb-4 w-full pt-4" radius="sm">
        <div className="flex flex-col sm:flex-row gap-4 justify-between items-center mb-4">
          <div className="flex gap-2 w-full sm:w-auto">
            <Input
              className="w-full sm:w-96"
              placeholder="Buscar por nombre, email, tipo o cargo..."
              startContent="🔍"
              value={searchTerm}
              onValueChange={handleSearch}
            />
          </div>
          <div className="flex gap-2 w-full sm:w-auto justify-end">
            <Button
              color={
                searchTerm || Object.keys(activeFilters).length > 0
                  ? "primary"
                  : "default"
              }
              variant="flat"
              onPress={handleClearFilters}
            >
              Borrar filtros{" "}
              {(searchTerm || Object.keys(activeFilters).length > 0) &&
                `(${(searchTerm ? 1 : 0) + Object.keys(activeFilters).length})`}
            </Button>
          </div>
        </div>
      </Card>

      {/* Contacts Table */}
      <Table
        removeWrapper
        aria-label="Contacts table"
        bottomContent={
          pages > 1 ? (
            <div className="flex w-full justify-center">
              <Pagination
                isCompact
                showControls
                showShadow
                color="primary"
                page={page}
                total={pages}
                onChange={(page) => setPage(page)}
              />
            </div>
          ) : null
        }
      >
        <TableHeader>
          <TableColumn>
            <FilterDropdown
              activeFilters={activeFilters}
              column="type"
              items={getUniqueValues("type")}
              sortConfig={sortConfig}
              title="Tipo de contacto"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn>
            <FilterDropdown
              activeFilters={activeFilters}
              column="name"
              items={getUniqueValues("name")}
              sortConfig={sortConfig}
              title="Nombre"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn>
            <FilterDropdown
              activeFilters={activeFilters}
              column="email"
              items={getUniqueValues("email")}
              sortConfig={sortConfig}
              title="Correo"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn>
            <FilterDropdown
              activeFilters={activeFilters}
              column="position"
              items={getUniqueValues("position")}
              sortConfig={sortConfig}
              title="Cargo"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn>
            <FilterDropdown
              activeFilters={activeFilters}
              column="project"
              items={getUniqueValues("project")}
              sortConfig={sortConfig}
              title="LID - Razon Social"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn>Acciones</TableColumn>
        </TableHeader>
        <TableBody
          isLoading={isLoading}
          items={items}
          loadingContent={<Spinner label="Cargando contactos..." />}
        >
          {(contact) => (
            <TableRow key={contact.id}>
              <TableCell>{contact.type}</TableCell>
              <TableCell>{contact.name}</TableCell>
              <TableCell className="underline">
                <a href={`mailto:${contact.email}`}>{contact.email}</a>
              </TableCell>
              <TableCell>{contact.position}</TableCell>
              <TableCell>{contact.project}</TableCell>
              <TableCell>
                <div className="flex gap-2">
                  <Button
                    isIconOnly
                    color="primary"
                    disabled={!canEditConfiguration}
                    size="sm"
                    variant="flat"
                    onPress={() => handleEditContact(contact)}
                  >
                    <Icon className="text-lg" icon="lucide:edit-3" />
                  </Button>
                  <Button
                    isIconOnly
                    color="danger"
                    disabled={!canEditConfiguration}
                    size="sm"
                    variant="flat"
                    onPress={() => handleDeleteContact(contact)}
                  >
                    <Icon className="text-lg" icon="lucide:trash" />
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>

      {/* Create Contact Modal */}
      <Modal
        isDismissable={false}
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
      >
        <ModalContent>
          {() => (
            <CreateContactForm
              onAddContact={handleAddContact}
              onClose={() => setIsCreateModalOpen(false)}
            />
          )}
        </ModalContent>
      </Modal>

      {/* Edit Contact Modal */}
      <Modal
        isDismissable={false}
        isOpen={isEditModalOpen}
        onClose={() => {
          console.log("Edit modal closing");
          setIsEditModalOpen(false);
          setSelectedContact(null);
        }}
      >
        <ModalContent>
          {() =>
            selectedContact && (
              <EditContactForm
                contact={selectedContact}
                onClose={() => {
                  setIsEditModalOpen(false);
                  setSelectedContact(null);
                }}
                onUpdateContact={handleUpdateContact}
              />
            )
          }
        </ModalContent>
      </Modal>

      {/* Delete Contact Modal */}
      <Modal
        isOpen={isDeleteModalOpen}
        onClose={() => {
          console.log("Delete modal closing");
          setIsDeleteModalOpen(false);
          setSelectedContact(null);
        }}
      >
        <ModalContent>
          {() =>
            selectedContact && (
              <DeleteContactModal
                contact={selectedContact}
                isLoading={isLoading}
                onClose={() => {
                  setIsDeleteModalOpen(false);
                  setSelectedContact(null);
                }}
                onConfirm={handleConfirmDelete}
              />
            )
          }
        </ModalContent>
      </Modal>
    </>
  );
}
