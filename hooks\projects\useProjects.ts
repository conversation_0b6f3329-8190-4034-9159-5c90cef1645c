"use client";

import { useState, useEffect } from "react";

import { API_ROUTES } from "@/lib/api";
import { axiosInstance } from "@/lib/axios";

export interface Implementation {
  id: number;
  name: string;
}

export interface ClientInfo {
  LID: number;
  client: string;
  aggregator: string;
}

export function useProjects() {
  const [implementations, setImplementations] = useState<Implementation[]>([]);
  const [clientInfo, setClientInfo] = useState<ClientInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchImplementationTypes = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axiosInstance.get(API_ROUTES.IMPLEMENTATION_TYPES);

      if (response.status === 200) {
        setImplementations(response.data);

        return response.data;
      } else {
        setError("Failed to fetch implementations");
      }
    } catch (err: any) {
      setError(err.message || "Failed to fetch implementations");
    } finally {
      setLoading(false);
    }
  };

  const fetchInfoClientes = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axiosInstance.get(API_ROUTES.INFO_CLIENTES);

      if (response.status === 200) {
        setClientInfo(response.data);

        return response.data;
      } else {
        setError("Failed to fetch client information");
      }
    } catch (err: any) {
      // Log error silently
      setError(err.message || "Failed to fetch client information");
    } finally {
      setLoading(false);
    }
  };

  return {
    implementations,
    clientInfo,
    loading,
    error,
    fetchImplementationTypes,
    fetchInfoClientes,
  };
}
