"use client";

import { useState, useEffect } from "react";

import { API_ROUTES } from "@/lib/api";
import { axiosInstance } from "@/lib/axios";

export interface Permission {
  id: number;
  name: string;
  codename: string;
  content_type: string;
}

export interface Group {
  id: number;
  name: string;
  permissions: string[];
}

export interface User {
  id: number;
  email: string;
  name: string;
  groups: Group[];
  user_permissions: Permission[];
  is_superuser: boolean;
  team_code: string;
}

export interface Role {
  id: number;
  name: string;
  permissions: Permission[];
}

export interface ProjectUser {
  user_id: number;
  name: string;
  email: string;
  group_id: string;
  group_name: string;
}

// export function useUsers() {
//   const [users, setUsers] = useState<User[]>([]);
//   const [permissions, setPermissions] = useState<Permission[]>([]);
//   const [roles, setRoles] = useState<Role[]>([]);
//   const [projectUsers, setProjectUsers] = useState<ProjectUser[]>([]);
//   const [loading, setLoading] = useState(true);
//   const [error, setError] = useState<string | null>(null);

//   const fetchUsers = async () => {
//     try {
//       setLoading(true);
//       setError(null);

//       const response = await axiosInstance.get(API_ROUTES.ALL_USERS);

//       if (response.status === 200) {
//         setUsers(response.data);
//       } else {
//         setError("Failed to fetch users");
//       }
//     } catch (err: any) {
//       setError(err.message || "Failed to fetch users");
//     } finally {
//       setLoading(false);
//     }
//   };

//   const fetchPermissions = async () => {
//     try {
//       const response = await axiosInstance.get(API_ROUTES.ALL_PERMISSIONS);

//       if (response.status === 200) {
//         setPermissions(response.data);
//       } else {
//         setError("Failed to fetch permissions");
//       }
//     } catch (err: any) {
//       setError(err.message || "Failed to fetch permissions");
//     }
//   };

//   const fetchRoles = async () => {
//     try {
//       const responseRoles = await axiosInstance.get(API_ROUTES.ALL_ROLES);

//       if (responseRoles.status === 200) {
//         setRoles(responseRoles.data);
//       } else {
//         setError("Failed to fetch roles");
//       }
//     } catch (err: any) {
//       setError(err.message || "Failed to fetch roles");
//     }
//   };

//   const fetchProjectUsers = async () => {
//     try {
//       setLoading(true);
//       setError(null);

//       const response = await axiosInstance.get(API_ROUTES.PROJECT_USERS);

//       if (response.status === 200) {
//         setProjectUsers(response.data);
//       } else {
//         setError("Failed to fetch project users");
//       }
//     } catch (err: any) {
//       setError(err.message || "Failed to fetch project users");
//     } finally {
//       setLoading(false);
//     }
//   };

//   const createRole = async (name: string, permissionIds: number[]) => {
//     try {
//       setLoading(true);
//       setError(null);

//       const response = await axiosInstance.post(API_ROUTES.CREATE_ROLE, {
//         name,
//         permissions: permissionIds,
//       });

//       if (response.status === 201 || response.status === 200) {
//         // Refresh the roles list
//         await fetchRoles();

//         return { success: true, data: response.data };
//       } else {
//         setError("Failed to create role");

//         return { success: false, error: "Failed to create role" };
//       }
//     } catch (err: any) {
//       const errorMsg = err.message || "Failed to create role";

//       setError(errorMsg);

//       return { success: false, error: errorMsg };
//     } finally {
//       setLoading(false);
//     }
//   };

//   const updateRole = async (roleId: number, permissionIds: number[]) => {
//     try {
//       setLoading(true);
//       setError(null);

//       const url = API_ROUTES.UPDATE_ROLE.replace("{id}", roleId.toString());
//       const response = await axiosInstance.post(url, {
//         permissions: permissionIds,
//       });

//       if (response.status === 200) {
//         // Refresh the roles list
//         await fetchRoles();

//         return { success: true, data: response.data };
//       } else {
//         setError("Failed to update role");

//         return { success: false, error: "Failed to update role" };
//       }
//     } catch (err: any) {
//       const errorMsg = err.message || "Failed to update role";

//       setError(errorMsg);

//       return { success: false, error: errorMsg };
//     } finally {
//       setLoading(false);
//     }
//   };

//   const deleteRole = async (roleId: number) => {
//     try {
//       setLoading(true);
//       setError(null);

//       const url = API_ROUTES.DELETE_ROLE.replace("{id}", roleId.toString());
//       const response = await axiosInstance.delete(url);

//       if (response.status === 204 || response.status === 200) {
//         // Refresh the roles list
//         await fetchRoles();

//         return { success: true };
//       } else {
//         setError("Failed to delete role");

//         return { success: false, error: "Failed to delete role" };
//       }
//     } catch (err: any) {
//       const errorMsg = err.message || "Failed to delete role";

//       setError(errorMsg);

//       return { success: false, error: errorMsg };
//     } finally {
//       setLoading(false);
//     }
//   };

//   const updateUserRole = async (user_id: number, roleId: number) => {
//     try {
//       setLoading(true);
//       setError(null);

//       const response = await axiosInstance.post(API_ROUTES.UPDATE_USER_ROLE, {
//         user_id,
//         role_id: roleId,
//       });

//       if (response.status === 200) {
//         // Refresh the users list
//         await fetchUsers();

//         return { success: true, data: response.data };
//       } else {
//         setError("Failed to update user role");

//         return { success: false, error: "Failed to update user role" };
//       }
//     } catch (err: any) {
//       const errorMsg = err.message || "Failed to update user role";

//       setError(errorMsg);

//       return { success: false, error: errorMsg };
//     }
//   };

//   const syncUsersWithCore = async () => {
//     try {
//       setLoading(true);
//       setError(null);

//       const response = await axiosInstance.post(API_ROUTES.SYNC_USERS_CORE);

//       if (response.status === 200) {
//         // Refresh the users list
//         await fetchUsers();

//         return { success: true, data: response.data };
//       } else {
//         setError("Failed to sync users with core");

//         return { success: false, error: "Failed to sync users with core" };
//       }
//     } catch (err: any) {
//       const errorMsg = err.message || "Failed to sync users with core";

//       setError(errorMsg);

//       return { success: false, error: errorMsg };
//     }
//   };

//   useEffect(() => {
//     fetchUsers();
//     fetchPermissions();
//     fetchRoles();
//     fetchProjectUsers();
//   }, []);

//   return {
//     users,
//     permissions,
//     roles,
//     projectUsers,
//     loading,
//     error,
//     refreshUsers: fetchUsers,
//     refreshRoles: fetchRoles,
//     refreshProjectUsers: fetchProjectUsers,
//     createRole,
//     updateRole,
//     deleteRole,
//     updateUserRole,
//     syncUsersWithCore,
//   };
// }
