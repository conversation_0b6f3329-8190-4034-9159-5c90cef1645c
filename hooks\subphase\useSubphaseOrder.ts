"use client";

import { useState } from "react";

import { API_ROUTES } from "@/lib/api";
import { axiosInstance } from "@/lib/axios";

export function useSubphaseOrder() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Decreases the order of a subphase (moves it up in the list)
   * @param subphaseId The ID of the subphase to decrease the order of
   * @returns An object indicating success or failure of the operation
   */
  const decreaseSubphaseOrder = async (subphaseId: number) => {
    try {
      setLoading(true);
      setError(null);

      const url = API_ROUTES.SUBPHASE_DECREASE.replace(
        "{subphase_id}",
        subphaseId.toString(),
      );
      const response = await axiosInstance.post(url);

      if (response.status === 200) {
        return { success: true, data: response.data };
      } else {
        const errorMsg = "Failed to decrease subphase order";

        setError(errorMsg);

        return { success: false, error: errorMsg };
      }
    } catch (err: any) {
      const errorMsg = err.message || "Failed to decrease subphase order";

      setError(errorMsg);

      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Increases the order of a subphase (moves it down in the list)
   * @param subphaseId The ID of the subphase to increase the order of
   * @returns An object indicating success or failure of the operation
   */
  const increaseSubphaseOrder = async (subphaseId: number) => {
    try {
      setLoading(true);
      setError(null);

      const url = API_ROUTES.SUBPHASE_INCREASE.replace(
        "{subphase_id}",
        subphaseId.toString(),
      );
      const response = await axiosInstance.post(url);

      if (response.status === 200) {
        return { success: true, data: response.data };
      } else {
        const errorMsg = "Failed to increase subphase order";

        setError(errorMsg);

        return { success: false, error: errorMsg };
      }
    } catch (err: any) {
      const errorMsg = err.message || "Failed to increase subphase order";

      setError(errorMsg);

      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    error,
    decreaseSubphaseOrder,
    increaseSubphaseOrder,
  };
}
