"use client";

import { Button, Input, Tab, Tabs } from "@heroui/react";
import { Key, useEffect, useState } from "react";

import { title } from "@/components/primitives";
import Templates from "@/components/configuration/templates";
import Subphase from "@/components/configuration/subphase";
import Fields from "@/components/configuration/fields";
import Rules from "@/components/configuration/rules";
import Emails from "@/components/configuration/emails";
import Notifications from "@/components/configuration/notifications";
import { useAuth } from "@/hooks/auth/useAuth";

export default function ConfiguracionPage() {
  const [selectedTab, setSelectedTab] = useState("templates");
  const [isCreating, setIsCreating] = useState(false);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Check if user has permission to edit configuration
  const { hasPermission } = useAuth();
  const [canEditConfiguration, setCanEditConfiguration] = useState(false);

  const handleTabChange = (key: Key) => {
    setSelectedTab(key.toString());
    setIsCreating(false); // Reset creating state when changing tabs
  };

  const handleCreateClick = () => {
    setIsCreating(true);
  };

  const handleRowsPerPageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = Number(e.target.value);

    if (!isNaN(value) && value > 0) {
      setRowsPerPage(value);
    }

    if (isNaN(value) || value <= 0) {
      setRowsPerPage(10);
    }
  };

  useEffect(() => {
    setCanEditConfiguration(hasPermission("editar_config"));
  }, [hasPermission]);

  return (
    <>
      <div className="flex justify-between items-center w-full">
        <h2 className={title({ size: "sm" })}>Configuración</h2>
      </div>

      <div className="mt-4 flex justify-between items-center w-full h-auto">
        <Tabs
          key="solid"
          aria-label="Opciones de configuración"
          selectedKey={selectedTab}
          variant="solid"
          onSelectionChange={handleTabChange}
        >
          <Tab key="subphase" title="Subfases" />
          <Tab key="fields" title="Campos" />
          <Tab key="rules" title="Reglas" />
          <Tab key="templates" title="Plantillas" />
          <Tab key="mails" title="Correos" />
          <Tab key="notifications" title="Notificaciones" />
          <Tab key="reporting" title="Reporting" />
        </Tabs>

        <div className="flex items-center gap-2">
          {selectedTab === "fields" && (
            <Input
              className="w-40"
              placeholder="Filas por página"
              type="number"
              onChange={handleRowsPerPageChange}
            />
          )}
          <Button
            color="primary"
            isDisabled={!canEditConfiguration}
            onPress={handleCreateClick}
          >
            {selectedTab === "subphase" && "Crear subfase"}
            {selectedTab === "fields" && "Crear campo"}
            {selectedTab === "rules" && "Crear regla"}
            {selectedTab === "templates" && "Crear plantilla"}
            {selectedTab === "mails" && "Crear correo"}
            {selectedTab === "notifications" && "Crear notificación"}
            {selectedTab === "reporting" && "Crear reporte"}
          </Button>
        </div>
      </div>

      {selectedTab === "notifications" && (
        <Notifications isCreating={isCreating} setIsCreating={setIsCreating} />
      )}

      {selectedTab === "mails" && (
        <Emails
          canEditConfiguration={canEditConfiguration}
          isCreating={isCreating}
          setIsCreating={setIsCreating}
        />
      )}

      {selectedTab === "rules" && (
        <Rules
          canEditConfiguration={canEditConfiguration}
          isCreating={isCreating}
          setIsCreating={setIsCreating}
        />
      )}

      {selectedTab === "subphase" && (
        <Subphase
          canEditConfiguration={canEditConfiguration}
          isCreating={isCreating}
          setIsCreating={setIsCreating}
        />
      )}

      {selectedTab === "fields" && (
        <Fields
          canEditConfiguration={canEditConfiguration}
          isCreating={isCreating}
          rowsPerPage={rowsPerPage}
          setIsCreating={setIsCreating}
        />
      )}

      {selectedTab === "templates" && (
        <Templates
          canEditConfiguration={canEditConfiguration}
          isCreating={isCreating}
          setIsCreating={setIsCreating}
        />
      )}

      {![
        "notifications",
        "mails",
        "rules",
        "subphase",
        "fields",
        "templates",
      ].includes(selectedTab) && (
        <div className="flex flex-col items-center justify-center w-full h-full p-4">
          <div className="p-4 text-center">
            <p className="text-lg mb-2">(´• ω •`)ﾉ</p>
            <h2 className="text-xl font-bold mb-1">Coming Soon!</h2>
            <p className="text-gray-600">
              This feature is still in development~
            </p>
            <p className="mt-2">(⁄ ⁄•⁄ω⁄•⁄ ⁄)</p>
          </div>
        </div>
      )}
    </>
  );
}
