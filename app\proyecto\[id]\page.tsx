"use client";

import type { Selection } from "@heroui/react";

import React, { useState, useMemo, useEffect } from "react";
import { <PERSON><PERSON>, Card, CardBody, Chip } from "@heroui/react";
import { Accordion, AccordionItem } from "@heroui/react";
import { Icon } from "@iconify/react";
import { useTheme } from "next-themes";
import { useParams } from "next/navigation";

import { ProgressTimeline } from "@/components/projects/project/progress-timeline";
import { getPhaseStyle, title } from "@/components/primitives";
import Field from "@/components/fields/field";
import { ContactsModal } from "@/components/contacts/contacts-modal";
import { projectSubphases, PHASES, Subphase } from "@/utils/dummy-data";
import { CommentsSection } from "@/components/comments/comments-section";
import { FieldProps } from "@/types/fields";

export default function ProyectoPage() {
  const { theme } = useTheme();
  const params = useParams();
  const projectId = params.id as string;

  const [isOpen, setIsOpen] = useState(false);
  const [selectedPhase, setSelectedPhase] = useState<string>(PHASES.COLLECTION);
  const [selectedSubphases, setSelectedSubphases] = useState<Set<string>>(
    new Set(["1"]),
  );
  const [editMode, setEditMode] = useState(false);
  const [cancelEditTrigger, setCancelEditTrigger] = useState(false);

  const [filterMilestones, setFilterMilestones] = useState(false);
  const [filterCompleted, setFilterCompleted] = useState(false);

  const [updatedFields, setUpdatedFields] = useState<Record<number, string>>(
    {},
  );

  // Save accordion state to consolidated localStorage
  const saveAccordionState = (phase: string, keys: Set<string>) => {
    if (typeof window !== "undefined") {
      // Get existing state or initialize new object
      const storageKey = "project_accordion_states";
      let allStates: Record<string, Record<string, string[]>> = {};

      try {
        const savedData = localStorage.getItem(storageKey);

        if (savedData) {
          allStates = JSON.parse(savedData);
        }
      } catch (e) {
        console.error("Error parsing saved states:", e);
      }

      // Ensure nested structure exists
      if (!allStates[projectId]) {
        allStates[projectId] = {};
      }

      // Save the state for this phase
      allStates[projectId][phase] = Array.from(keys);

      // Save back to localStorage
      localStorage.setItem(storageKey, JSON.stringify(allStates));
    }
  };

  // Load accordion state from consolidated localStorage
  const loadAccordionState = (phase: string): Set<string> => {
    if (typeof window !== "undefined") {
      const storageKey = "project_accordion_states";

      try {
        const savedData = localStorage.getItem(storageKey);

        if (savedData) {
          const allStates = JSON.parse(savedData);

          if (allStates[projectId] && allStates[projectId][phase]) {
            return new Set(allStates[projectId][phase]);
          }
        }
      } catch (e) {
        console.error("Error parsing saved accordion state:", e);
      }
    }

    // Return all subphases selected if no saved state
    return new Set(
      projectSubphases
        .filter((subphase) => subphase.phase === phase)
        .map((subphase) => String(subphase.id)),
    );
  };

  const handlePhaseSelect = (phase: string) => {
    setSelectedPhase(phase);
    // Load saved state for the new phase
    const savedState = loadAccordionState(phase);

    setSelectedSubphases(savedState);
  };

  // Handler for accordion selection change
  const handleSelectionChange = (keys: Selection) => {
    // Convert the Selection type to Set<string>
    if (keys === "all") {
      // If "all" is selected, include all subphase IDs
      const allIds = new Set(
        filteredSubphases.map((subphase) => String(subphase.id)),
      );

      setSelectedSubphases(allIds);
      saveAccordionState(selectedPhase, allIds);
    } else {
      // Otherwise convert the keys object to a Set
      const newKeys = new Set(keys as Set<string>);

      setSelectedSubphases(newKeys);
      saveAccordionState(selectedPhase, newKeys);
    }
  };

  const handleSave = async () => {
    try {
      if (!editMode) {
        setEditMode(true);

        return;
      }
      console.log(JSON.stringify(updatedFields));

      setUpdatedFields({});
      setEditMode(false);
    } catch (error) {
      console.error("Error saving fields:", error);
    }
  };

  const cancelSave = () => {
    setCancelEditTrigger(!cancelEditTrigger);
    setEditMode(false);
  };

  // Filter subphases based on the selected phase and sort by order
  const filteredSubphases = useMemo(() => {
    return projectSubphases
      .filter((subphase) => subphase.phase === selectedPhase)
      .sort((a, b) => a.order - b.order);
  }, [selectedPhase]);

  const subphaseCompletition = (subphase: Subphase) => {
    let completed = 0;
    let inProgress = 0;
    let notStarted = 0;

    enum TaskState {
      NOT_COMPLETED = "pendiente",
      IN_PROGRESS = "en_progreso",
      COMPLETED = "completado",
    }

    const tareaOptions = [
      { key: "pendiente", label: "Pendiente", value: TaskState.NOT_COMPLETED },
      {
        key: "en_progreso",
        label: "En progreso",
        value: TaskState.IN_PROGRESS,
      },
      { key: "completado", label: "Completado", value: TaskState.COMPLETED },
      { key: "cancelado", label: "Cancelado", value: TaskState.COMPLETED },
    ];

    subphase.fields.forEach((field) => {
      switch (field.type) {
        case "Informativo":
          if (field.value) {
            completed++;
          }
          break;
        case "Selección":
          if (field.value) {
            completed++;
          }
          break;
        case "Tarea":
        case "Documento":
          const selectedOption = tareaOptions.find(
            (option) => option.key === field.value,
          );

          if (!selectedOption) {
            notStarted++;
          }
          if (selectedOption?.value === TaskState.COMPLETED) {
            completed++;
          }
          if (selectedOption?.value === TaskState.IN_PROGRESS) {
            inProgress++;
          }
          if (selectedOption?.value === TaskState.NOT_COMPLETED) {
            notStarted++;
          }
          break;
        case "Subtarea":
          if (field.subtasks) {
            field.subtasks.forEach((subtask) => {
              if (subtask.value) {
                completed++;
              } else {
                notStarted++;
              }
            });
          }
          break;
        default:
          break;
      }
    });

    const totalFields = subphase.fields.length;
    const completedPercentage = Math.round((completed / totalFields) * 100);

    return (
      <Chip
        color={
          completed === totalFields
            ? "success"
            : completed > 0 || inProgress > 0
              ? "warning"
              : "danger"
        }
        size="sm"
        variant="solid"
      >
        {completed === 100
          ? `Completado: ${completedPercentage}%`
          : `${completedPercentage}%`}
      </Chip>
    );
  };

  const isFieldCompleted = (field: FieldProps): boolean => {
    enum TaskState {
      NOT_COMPLETED = "pendiente",
      IN_PROGRESS = "en_progreso",
      COMPLETED = "completado",
    }

    const taskOptions = [
      { key: "pendiente", value: TaskState.NOT_COMPLETED },
      { key: "en_progreso", value: TaskState.IN_PROGRESS },
      { key: "completado", value: TaskState.COMPLETED },
      { key: "cancelado", value: TaskState.COMPLETED },
    ];

    switch (field.type) {
      case "Informativo":
      case "Selección":
        return !!field.value;
      case "Tarea":
      case "Documento":
        const selectedOption = taskOptions.find(
          (opt) => opt.key === field.value,
        );

        return selectedOption?.value === TaskState.COMPLETED;
      case "Subtarea":
        return field.subtasks?.every((subtask) => !!subtask.value) ?? false;
      default:
        return false;
    }
  };

  // Initialize state from localStorage when component mounts
  useEffect(() => {
    const savedState = loadAccordionState(selectedPhase);

    setSelectedSubphases(savedState);
  }, [projectId]);

  // Reset selectedSubphases when the phase changes
  useEffect(() => {
    // Load saved state when phase changes
    const savedState = loadAccordionState(selectedPhase);

    cancelSave();
    setUpdatedFields({});

    setSelectedSubphases(savedState);
  }, [selectedPhase]);

  const tempState = "Prevista";

  return (
    <div>
      <div className="flex justify-between items-center mx-auto mb-4">
        <div className="flex items-center gap-3">
          <h2 className={title({ size: "sm" })}>1516 - Infini</h2>
          <div
            className={`text-center p-1 rounded-lg ${getPhaseStyle(
              "COLLECTION",
              theme === "dark",
            )}`}
          >
            COLLECTION
          </div>
          <div className="flex justify-center">
            {tempState === "Prevista" ? (
              <Icon icon="lucide:clock" width={18} />
            ) : tempState === "En curso" ? (
              <Icon icon="lucide:play-circle" width={18} />
            ) : tempState === "On hold" ? (
              <Icon icon="lucide:pause-circle" width={18} />
            ) : tempState === "Cancelado" ? (
              <Icon icon="lucide:x-circle" width={18} />
            ) : (
              <Icon icon="lucide:circle" width={18} />
            )}
          </div>
        </div>
        <div className="flex gap-2">
          <Button
            isIconOnly
            color="primary"
            startContent={<Icon icon="lucide:users" />}
            variant="solid"
            onPress={() => setIsOpen(true)}
          />
          <Button
            color="primary"
            startContent={<Icon icon="lucide:file-text" />}
            variant="solid"
          >
            Reports
          </Button>
        </div>
      </div>

      <Card className="mx-auto" radius={"sm"}>
        <CardBody>
          <div className="grid grid-cols-2 md:grid-cols-9 gap-4">
            <div>
              <h3 className="text-sm text-default-500">Agregador:</h3>
              <p className="text-sm">Local</p>
            </div>
            <div>
              <h3 className="text-sm text-default-500">Tipología:</h3>
              <p className="text-sm">Existente</p>
            </div>
            <div>
              <h3 className="text-sm text-default-500">Mes Live:</h3>
              <p className="text-sm">05/2025</p>
            </div>
            <div>
              <h3 className="text-sm text-default-500">Tests:</h3>
              <p className="text-sm">08/2026, 09/2024</p>
            </div>
            <div>
              <h3 className="text-sm text-default-500">Coordinador:</h3>
              <p className="text-sm">Ana Moreno</p>
            </div>
            <div>
              <h3 className="text-sm text-default-500">Implementador 1:</h3>
              <p className="text-sm">Juanjo Pedro</p>
            </div>
            <div>
              <h3 className="text-sm text-default-500">Implementador 2:</h3>
              <p className="text-sm">Pedro Juanjo</p>
            </div>
            <div>
              <h3 className="text-sm text-default-500">Incubadora:</h3>
              <p className="text-sm">Pedro Gonzalez</p>
            </div>
            <div>
              <h3 className="text-sm text-default-500">Backup:</h3>
              <p className="text-sm">María Ramos</p>
            </div>
          </div>
        </CardBody>
      </Card>
      {/* {JSON.stringify(updatedFields)} */}
      <ProgressTimeline
        isEditMode={editMode}
        selectedPhase={selectedPhase}
        onPhaseSelect={handlePhaseSelect}
      />

      <Accordion
        aria-label="Subphases accordion"
        className="mt-2"
        defaultSelectedKeys={"all"}
        itemClasses={{
          title: "text-2xl font-bold",
        }}
        selectedKeys={selectedSubphases}
        selectionMode="multiple"
        onSelectionChange={handleSelectionChange}
      >
        {/* filtro por milestone */}
        {filteredSubphases.map((subphase) => (
          <AccordionItem
            key={subphase.id}
            aria-label={`Subfase ${subphase.order}`}
            className="w-full mb-4"
            title={
              <div className="flex items-center gap-2">
                <span>{subphase.title}</span>
                {subphaseCompletition(subphase)}
              </div>
            }
          >
            {subphase.fields
              .filter((field) => {
                if (filterMilestones) {
                  return !field.milestone;
                }

                return true;
              })
              .filter((field) => {
                if (filterCompleted) {
                  return !isFieldCompleted(field);
                }

                return true;
              })
              .map((field) => (
                <Field
                  key={field.id}
                  cancelEditTrigger={cancelEditTrigger}
                  description={field.description}
                  edit={editMode}
                  id={field.id}
                  milestone={field.milestone}
                  observation={field.observation || ""}
                  options={field.options}
                  subtasks={field.subtasks}
                  title={field.title}
                  type={field.type}
                  value={field.value}
                  onChange={(id, value) => {
                    setUpdatedFields((prev) => ({
                      ...prev,
                      [id]: value,
                    }));
                  }}
                />
              ))}
          </AccordionItem>
        ))}
      </Accordion>

      <CommentsSection currentPhase={selectedPhase} projectId={projectId} />

      <ContactsModal
        isOpen={isOpen}
        selectedProject={Number(projectId)}
        onClose={() => setIsOpen(false)}
      />

      {editMode ? (
        <Button
          className="fixed bottom-4 right-20 z-50"
          color="danger"
          size="lg"
          variant="light"
          onPress={cancelSave}
        >
          Cancelar
        </Button>
      ) : null}

      <Button
        isIconOnly
        className="fixed bottom-4 right-4 z-50"
        color="primary"
        size="lg"
        startContent={
          <Icon icon={editMode ? "lucide:save" : "lucide:edit-3"} />
        }
        variant="solid"
        onPress={handleSave}
      />

      <div
        className={`fixed bottom-32 right-[-65px] z-50 min-w-6 min-h-32 ${
          theme === "dark" ? "bg-gray-800" : "bg-slate-600"
        } bg-opacity-75 rounded-lg shadow-lg p-4 hover:right-[-10px] transition-all duration-300 flex flex-col items-center gap-3`}
      >
        <Button
          isIconOnly
          color="danger"
          size="lg"
          startContent={
            filterMilestones ? (
              <svg
                className="lucide lucide-milestone-icon"
                fill="none"
                height="24"
                stroke="currentColor"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                viewBox="0 0 24 24"
                width="24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M12 13v8" />
                <path d="M12 3v3" />
                <path d="M4 6a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h13a2 2 0 0 0 1.152-.365l3.424-2.317a1 1 0 0 0 0-1.635l-3.424-2.318A2 2 0 0 0 17 6z" />
              </svg>
            ) : (
              <svg
                className="lucide lucide-milestone-icon"
                fill="none"
                height="24"
                stroke="currentColor"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                viewBox="0 0 24 24"
                width="24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M12 13v8" />
                <path d="M12 3v3" />
                <path d="M4 6a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h13a2 2 0 0 0 1.152-.365l3.424-2.317a1 1 0 0 0 0-1.635l-3.424-2.318A2 2 0 0 0 17 6z" />
                <path d="M3 3l18 18" stroke="white" strokeWidth="4" />
                <path d="M3 3l18 18" />
              </svg>
            )
          }
          variant="solid"
          onPress={() => setFilterMilestones(!filterMilestones)}
        />

        <Button
          isIconOnly
          color="primary"
          size="lg"
          startContent={
            !filterCompleted ? (
              <svg
                className="size-6"
                fill="none"
                stroke="currentColor"
                strokeWidth="1.5"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path d="M3 3l18 18" stroke="white" strokeWidth="4" />
                <path d="M3 3l18 18" />
              </svg>
            ) : (
              <svg
                className="size-6"
                fill="none"
                stroke="currentColor"
                strokeWidth="1.5"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            )
          }
          variant="solid"
          onPress={() => setFilterCompleted(!filterCompleted)}
        />
      </div>
    </div>
  );
}
