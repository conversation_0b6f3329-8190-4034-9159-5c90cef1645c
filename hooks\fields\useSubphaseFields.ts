"use client";

import { useState } from "react";

import { API_ROUTES } from "@/lib/api";
import { axiosInstance } from "@/lib/axios";

export interface SubphaseField {
  subphase_id: number;
  subphase_name: string;
  fields_count: number;
  fields: string[];
}

export function useSubphaseFields() {
  const [subphaseFields, setSubphaseFields] = useState<SubphaseField[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchSubphaseFields = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axiosInstance.get(API_ROUTES.SUBPHASES_FIELDS);

      if (response.status === 200) {
        setSubphaseFields(response.data);
        return response.data;
      } else {
        setError("Failed to fetch subphase fields");
      }
    } catch (err: any) {
      setError(err.message || "Failed to fetch subphase fields");
    } finally {
      setLoading(false);
    }
  };

  // Helper function to get fields count for a specific subphase
  const getFieldsCountBySubphaseId = (subphaseId: number): number => {
    const subphase = subphaseFields.find(
      (item) => item.subphase_id === subphaseId
    );
    return subphase?.fields_count || 0;
  };

  // Helper function to get fields for a specific subphase
  const getFieldsBySubphaseId = (subphaseId: number): string[] => {
    const subphase = subphaseFields.find(
      (item) => item.subphase_id === subphaseId
    );
    return subphase?.fields || [];
  };

  // Helper function to check if a subphase has fields
  const hasFields = (subphaseId: number): boolean => {
    return getFieldsCountBySubphaseId(subphaseId) > 0;
  };

  return {
    subphaseFields,
    loading,
    error,
    fetchSubphaseFields,
    getFieldsCountBySubphaseId,
    getFieldsBySubphaseId,
    hasFields,
  };
}
