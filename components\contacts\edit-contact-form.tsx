"use client";
import { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Input,
  Select,
  SelectItem,
  Form,
  Autocomplete,
  AutocompleteItem,
} from "@heroui/react";
import { useQuery } from "@apollo/client";

import { GET_ALL_BASIC_PROJECTS_INFO } from "@/graphql/operations/projects";

interface Contact {
  id: number;
  type: string;
  name: string;
  email: string;
  position: string;
  project: string;
}

interface EditContactFormProps {
  onClose: () => void;
  onUpdateContact: (contact: Contact) => void;
  contact: Contact;
}

const contactTypes = [
  "Empresa",
  "Agregador",
  "Add-on",
  "OOS",
  "Impuestos",
  "SS",
  "GL File",
];

const EditContactForm = ({
  onClose,
  onUpdateContact,
  contact,
}: EditContactFormProps) => {
  const { data: allProjects } = useQuery(GET_ALL_BASIC_PROJECTS_INFO);

  const [formData, setFormData] = useState({
    id: contact.id,
    type: contact.type,
    name: contact.name,
    email: contact.email,
    position: contact.position,
    project: contact.project,
  });

  const [selectedProjectKey, setSelectedProjectKey] = useState<string | null>(
    null
  );

  // Initialize form data when contact changes
  useEffect(() => {
    setFormData({
      id: contact.id,
      type: contact.type,
      name: contact.name,
      email: contact.email,
      position: contact.position,
      project: contact.project,
    });

    // Find the project ID based on the project name
    if (allProjects?.allProjects && contact.project) {
      const project = allProjects.allProjects.find(
        (p: any) => p.alias === contact.project
      );
      if (project) {
        setSelectedProjectKey(project.id.toString());
      }
    }
  }, [contact, allProjects]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
  ) => {
    const { name, value } = e.target;

    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleProjectChange = (key: string | null) => {
    setSelectedProjectKey(key);
    if (key && allProjects?.allProjects) {
      const selectedProject = allProjects.allProjects.find(
        (project: any) => project.id.toString() === key,
      );
      if (selectedProject) {
        setFormData((prev) => ({
          ...prev,
          project: selectedProject.alias,
        }));
      }
    } else {
      setFormData((prev) => ({
        ...prev,
        project: "",
      }));
    }
  };

  const handleSubmit = () => {
    // Console log the form data when updating a contact
    console.log("Updating contact with data:", formData);
    console.log("Selected project:", selectedProjectKey);

    // Here you would typically make an API call to update the contact
    // For demo purposes, we'll just call the update function
    onUpdateContact(formData);
  };

  return (
    <>
      <ModalHeader>Editar Contacto</ModalHeader>
      <Form
        onSubmit={(e) => {
          e.preventDefault();
          handleSubmit();
        }}
      >
        <ModalBody className="w-full">
          <div className="flex flex-col gap-4 w-full">
            <div className="w-full">
              <label
                className="block text-sm font-medium mb-1"
                htmlFor="contact-type"
              >
                Tipo de contacto
              </label>
              <Select
                className="w-full"
                id="contact-type"
                name="type"
                placeholder="Selecciona un tipo"
                selectionMode="single"
                selectedKeys={[formData.type]}
                onChange={handleChange}
              >
                {contactTypes.map((type) => (
                  <SelectItem key={type}>{type}</SelectItem>
                ))}
              </Select>
            </div>
            <div>
              <label
                className="block text-sm font-medium mb-1"
                htmlFor="contact-name"
              >
                Nombre
              </label>
              <Input
                className="w-full"
                id="contact-name"
                name="name"
                placeholder="Nombre completo"
                value={formData.name}
                onChange={handleChange}
              />
            </div>
            <div>
              <label
                className="block text-sm font-medium mb-1"
                htmlFor="contact-email"
              >
                Correo electrónico
              </label>
              <Input
                className="w-full"
                id="contact-email"
                name="email"
                placeholder="<EMAIL>"
                type="email"
                value={formData.email}
                onChange={handleChange}
              />
            </div>
            <div>
              <label
                className="block text-sm font-medium mb-1"
                htmlFor="contact-position"
              >
                Cargo
              </label>
              <Input
                className="w-full"
                id="contact-position"
                name="position"
                placeholder="Cargo o posición"
                value={formData.position}
                onChange={handleChange}
              />
            </div>
            <div>
              <label
                className="block text-sm font-medium mb-1"
                htmlFor="contact-project"
              >
                Implementación
              </label>
              <Autocomplete
                className="w-full"
                id="contact-project"
                inputValue={formData.project}
                placeholder="Buscar implementación..."
                selectedKey={selectedProjectKey}
                onInputChange={(value) => {
                  setFormData((prev) => ({
                    ...prev,
                    project: value,
                  }));
                }}
                onSelectionChange={(key) => {
                  handleProjectChange(key as string);
                }}
              >
                {allProjects?.allProjects?.map((project: any) => (
                  <AutocompleteItem
                    key={project.id.toString()}
                    textValue={project.alias}
                  >
                    <div className="text-default-700">
                      <div className="font-medium">{project.alias}</div>
                      <div className="text-sm text-default-500">
                        {project.aggregator}
                      </div>
                      <div className="text-xs text-default-400">
                        LID: {project.lid}
                      </div>
                    </div>
                  </AutocompleteItem>
                ))}
              </Autocomplete>
            </div>
          </div>
        </ModalBody>
        <ModalFooter className="justify-end w-full">
          <Button
            color="danger"
            type="button"
            variant="light"
            onPress={onClose}
          >
            Cancelar
          </Button>
          <Button color="primary" type="submit">
            Actualizar
          </Button>
        </ModalFooter>
      </Form>
    </>
  );
};

export default EditContactForm;
