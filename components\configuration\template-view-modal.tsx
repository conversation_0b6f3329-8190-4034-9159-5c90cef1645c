"use client";

import React, { useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardBody,
  Chip,
  Spinner,
} from "@heroui/react";
import { Icon } from "@iconify/react";

import { useTemplateDetail } from "@/hooks/templates/useTemplateDetail";

interface Template {
  id: number;
  title: string;
  description: string;
  is_active: boolean;
}

interface TemplateViewModalProps {
  isOpen: boolean;
  template: Template | null;
  onClose: () => void;
}

export function TemplateViewModal({
  isOpen,
  template,
  onClose,
}: TemplateViewModalProps) {
  const {
    templateDetail,
    loading,
    error,
    fetchTemplateDetail,
    clearTemplateDetail,
  } = useTemplateDetail();

  useEffect(() => {
    if (isOpen && template) {
      fetchTemplateDetail(template.id.toString());
    } else if (!isOpen) {
      clearTemplateDetail();
    }
  }, [is<PERSON><PERSON>, template]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("es-ES", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const renderFields = () => {
    if (!templateDetail?.full_definition?.fields?.length) {
      return (
        <div className="text-center py-8">
          <Icon
            className="text-4xl text-default-400 mb-2"
            icon="lucide:folder-open"
          />
          <p className="text-default-500">No hay campos asociados</p>
        </div>
      );
    }

    return (
      <div className="space-y-3">
        {templateDetail.full_definition.fields.map((field) => (
          <Card key={field.id} className="border border-default-200">
            <CardBody className="p-4">
              <div className="flex justify-between items-start mb-2">
                <h4 className="font-medium text-lg">{field.name}</h4>
                <div className="flex gap-2">
                  <Chip color="primary" size="sm" variant="flat">
                    {field.type}
                  </Chip>
                  {field.is_milestone && (
                    <Chip color="warning" size="sm" variant="flat">
                      Hito
                    </Chip>
                  )}
                </div>
              </div>
              {field.description && (
                <p className="text-default-600 mb-2">{field.description}</p>
              )}
              <div className="flex gap-4 text-sm text-default-500">
                <span>Subfase: {field.subphase}</span>
                <span>Peso: {field.weight}</span>
              </div>
            </CardBody>
          </Card>
        ))}
      </div>
    );
  };

  const renderRules = () => {
    if (!templateDetail?.full_definition?.rules?.length) {
      return (
        <div className="text-center py-8">
          <Icon
            className="text-4xl text-default-400 mb-2"
            icon="lucide:folder-open"
          />
          <p className="text-default-500">No hay reglas asociadas</p>
        </div>
      );
    }

    return (
      <div className="space-y-3">
        {templateDetail.full_definition.rules.map((rule) => (
          <Card key={rule.id} className="border border-default-200">
            <CardBody className="p-4">
              <div className="flex justify-between items-start mb-2">
                <h4 className="font-medium text-lg">{rule.name}</h4>
                <Chip color="secondary" size="sm" variant="flat">
                  {rule.action}
                </Chip>
              </div>
              {rule.description && (
                <p className="text-default-600 mb-3">{rule.description}</p>
              )}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-default-700">
                    Campo origen:
                  </span>
                  <p className="text-default-600">{rule.origin_field}</p>
                </div>
                <div>
                  <span className="font-medium text-default-700">
                    Campo destino:
                  </span>
                  <p className="text-default-600">{rule.target_field}</p>
                </div>
                <div className="md:col-span-2">
                  <span className="font-medium text-default-700">
                    Condición:
                  </span>
                  <p className="text-default-600">{rule.condition}</p>
                </div>
              </div>
            </CardBody>
          </Card>
        ))}
      </div>
    );
  };

  const renderNotifications = () => {
    if (!templateDetail?.full_definition?.notifications?.length) {
      return (
        <div className="text-center py-8">
          <Icon
            className="text-4xl text-default-400 mb-2"
            icon="lucide:folder-open"
          />
          <p className="text-default-500">No hay notificaciones asociadas</p>
        </div>
      );
    }

    return (
      <div className="space-y-3">
        {templateDetail.full_definition.notifications.map((notification) => (
          <Card key={notification.id} className="border border-default-200">
            <CardBody className="p-4">
              <div className="flex justify-between items-start mb-2">
                <h4 className="font-medium text-lg">{notification.name}</h4>
                <Chip color="success" size="sm" variant="flat">
                  Notificación
                </Chip>
              </div>
              {notification.description && (
                <p className="text-default-600 mb-3">
                  {notification.description}
                </p>
              )}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-default-700">
                    Campo disparador:
                  </span>
                  <p className="text-default-600">
                    {notification.trigger_field}
                  </p>
                </div>
                <div>
                  <span className="font-medium text-default-700">
                    Condición:
                  </span>
                  <p className="text-default-600">
                    {notification.trigger_condition}
                  </p>
                </div>
                <div className="md:col-span-2">
                  <span className="font-medium text-default-700">Valor:</span>
                  <p className="text-default-600">{notification.value}</p>
                </div>
              </div>
            </CardBody>
          </Card>
        ))}
      </div>
    );
  };

  return (
    <Modal
      classNames={{
        base: "max-h-[90vh]",
        body: "max-h-[calc(90vh-120px)] overflow-auto p-0",
      }}
      isOpen={isOpen}
      scrollBehavior="inside"
      size="5xl"
      onClose={onClose}
    >
      <ModalContent>
        {(onClose) => (
          <>
            <ModalHeader className="flex flex-col gap-1 pb-2 border-b">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="text-xl font-medium">{template?.title}</h3>
                  {template?.description && (
                    <p className="text-small text-default-500 mt-1">
                      {template.description}
                    </p>
                  )}
                </div>
                <Chip
                  color={template?.is_active ? "success" : "default"}
                  size="sm"
                  variant="flat"
                >
                  {template?.is_active ? "Activa" : "Inactiva"}
                </Chip>
              </div>
              {templateDetail && (
                <div className="flex gap-4 text-xs text-default-500 mt-2">
                  <span>Creado: {formatDate(templateDetail.created_at)}</span>
                  <span>
                    Actualizado: {formatDate(templateDetail.updated_at)}
                  </span>
                </div>
              )}
            </ModalHeader>
            <ModalBody className="p-6">
              {loading ? (
                <div className="flex justify-center items-center py-12">
                  <Spinner size="lg" />
                </div>
              ) : error ? (
                <div className="text-center py-12">
                  <Icon
                    className="text-4xl text-danger mb-2"
                    icon="lucide:alert-circle"
                  />
                  <p className="text-danger">
                    Error al cargar los detalles: {error}
                  </p>
                </div>
              ) : (
                <Tabs aria-label="Template details" className="w-full">
                  <Tab
                    key="fields"
                    title={
                      <div className="flex items-center space-x-2">
                        <Icon icon="lucide:list" />
                        <span>
                          Campos (
                          {templateDetail?.full_definition?.fields?.length || 0}
                          )
                        </span>
                      </div>
                    }
                  >
                    <div className="py-4">{renderFields()}</div>
                  </Tab>
                  <Tab
                    key="rules"
                    title={
                      <div className="flex items-center space-x-2">
                        <Icon icon="lucide:settings" />
                        <span>
                          Reglas (
                          {templateDetail?.full_definition?.rules?.length || 0})
                        </span>
                      </div>
                    }
                  >
                    <div className="py-4">{renderRules()}</div>
                  </Tab>
                  <Tab
                    key="notifications"
                    title={
                      <div className="flex items-center space-x-2">
                        <Icon icon="lucide:bell" />
                        <span>
                          Notificaciones (
                          {templateDetail?.full_definition?.notifications
                            ?.length || 0}
                          )
                        </span>
                      </div>
                    }
                  >
                    <div className="py-4">{renderNotifications()}</div>
                  </Tab>
                </Tabs>
              )}
            </ModalBody>
            <ModalFooter>
              <Button variant="light" onPress={onClose}>
                Cerrar
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
}
