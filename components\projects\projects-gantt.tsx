import React, {
  useEffect,
  useRef,
  useState,
  use<PERSON><PERSON>back,
  useMemo,
} from "react";
import {
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Tooltip,
  ScrollShadow,
  ButtonGroup,
  Button,
  Pagination,
  Card,
  Input,
  DateRangePicker,
  DateValue,
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { useTheme } from "next-themes";
import { RangeValue } from "@react-types/shared";
import { useQuery } from "@apollo/client";

import PhaseCountCard from "./phase-count-card";
import { FilterDropdown } from "./projects-table/filter-dropdown";

import { Project as ProjectBase } from "@/types/projects";
import { UserAvatar } from "@/utils/avatars";
import { GET_ALL_PROJECTS_TIMETABLE } from "@/graphql/operations/projects";

// Helper function for drawing rounded rectangles
function drawRoundedRect(
  ctx: CanvasRenderingContext2D,
  x: number,
  y: number,
  width: number,
  height: number,
  radius: number,
) {
  ctx.beginPath();
  ctx.moveTo(x + radius, y);
  ctx.lineTo(x + width - radius, y);
  ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
  ctx.lineTo(x + width, y + height - radius);
  ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
  ctx.lineTo(x + radius, y + height);
  ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
  ctx.lineTo(x, y + radius);
  ctx.quadraticCurveTo(x, y, x + radius, y);
  ctx.closePath();
}

interface Project extends ProjectBase {
  startDate: string; // YYYY-MM-DD
  endDate: string; // YYYY-MM-DD
  implementacion2?: string;
}

type TimeScale = "week" | "month";

interface TimePeriod {
  key: string;
  label: string;
  width: number;
  tooltip?: string;
  isCurrentDay?: boolean;
  isCurrentWeek?: boolean;
  isCurrentMonth?: boolean;
  monthKey?: string;
  startDate?: string;
  endDate?: string;
}

const ProjectsGantt = () => {
  const { theme } = useTheme();
  const rowCount = 10;

  const [projects, setProjects] = useState<Project[]>([]);
  const [timeScale, setTimeScale] = useState<TimeScale>("month");
  const [statusFilter, setStatusFilter] = useState<string>("");
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredProjects, setFilteredProjects] = useState<Project[]>([]);
  const [activeFilters, setActiveFilters] = useState<Record<string, string[]>>(
    {},
  );
  const [dateRange, setDateRange] = useState<RangeValue<DateValue> | null>(
    null,
  );
  const [page, setPage] = useState(1);
  const [sortConfig, setSortConfig] = useState<{
    column: string;
    direction: "asc" | "desc";
  } | null>(null);

  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const headerCanvasRef = useRef<HTMLCanvasElement>(null);

  const redrawRef = useRef(true);

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    applyFiltersAndSort(value, activeFilters, sortConfig);
  };
  const pages = Math.ceil(filteredProjects.length / rowCount);

  const { loading, data } = useQuery(GET_ALL_PROJECTS_TIMETABLE);

  useEffect(() => {
    if (data && data.allProjects) {
      const fetchedProjects = data.allProjects.map((project: any) => {
        const impl1Name = project.implementer1
          ? `${project.implementer1.firstName} ${project.implementer1.lastName}`
          : "N/A";

        const impl2Name = project.implementer2
          ? `${project.implementer2.firstName} ${project.implementer2.lastName}`
          : "N/A";

        return {
          id: project.id || "N/A",
          alias: project.alias || "N/A",
          agregador: project.aggregator || "N/A",
          tipologia: project.implementationType || "N/A",
          implementacion: impl1Name,
          implementacion2: impl2Name,
          startDate: project.startInitialDate || "N/A",
          endDate: project.goliveFinalDate || "N/A",
          estado: project.estado || "N/A",
        };
      });

      setProjects(fetchedProjects);
      setFilteredProjects(fetchedProjects);
      redrawRef.current = true;
    }
  }, [data]);

  useEffect(() => {
    setFilteredProjects(projects);
  }, [projects]);

  useEffect(() => {
    if (!statusFilter) {
      setFilteredProjects(projects);

      return;
    }

    const filtered = projects.filter(
      (project) => project.estado === statusFilter,
    );

    setFilteredProjects(filtered);
    redrawRef.current = true;
  }, [statusFilter, projects]);

  const getWeekNumber = useCallback((date: Date) => {
    const target = new Date(date.valueOf());
    const dayNum = (date.getUTCDay() + 6) % 7;

    target.setUTCDate(target.getUTCDate() - dayNum + 3);
    const firstThursday = target.valueOf();

    target.setUTCMonth(0, 1);
    if (target.getUTCDay() !== 4) {
      target.setUTCMonth(0, 1 + ((4 - target.getUTCDay() + 7) % 7));
    }

    return 1 + Math.ceil((firstThursday - target.valueOf()) / 604800000);
  }, []);

  const formatDateDDMM = useCallback((dateStr: string) => {
    const date = new Date(dateStr);

    return `${date.getDate().toString().padStart(2, "0")}/${(
      date.getMonth() + 1
    )
      .toString()
      .padStart(2, "0")}`;
  }, []);

  const today = useMemo(() => new Date().toISOString().split("T")[0], []);
  const todayDate = useMemo(() => new Date(), []);

  const generateDates = useCallback(() => {
    const dates = [];
    const startDate = new Date(todayDate);

    startDate.setMonth(startDate.getMonth() - 6);
    startDate.setDate(1);

    const endDate = new Date(todayDate);

    endDate.setMonth(endDate.getMonth() + 6);
    endDate.setDate(0);

    const dayDiff =
      Math.ceil(
        (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24),
      ) + 1;

    interface MonthData {
      count: number;
      year: number;
      monthName: string;
      startDate: string;
      endDate: string;
    }

    interface WeekData {
      count: number;
      year: number;
      weekNum: number;
      monthName: string;
      startDate: string;
      endDate: string;
      dates: string[];
    }

    const months: Record<string, MonthData> = {};
    const weeks: Record<string, WeekData> = {};

    dates.length = dayDiff;

    for (let i = 0; i < dayDiff; i++) {
      const date = new Date(startDate);

      date.setDate(date.getDate() + i);

      const year = date.getFullYear();
      const monthName = date.toLocaleString("default", { month: "short" });
      const monthKey = `${monthName}-${year}`;

      const weekNum = getWeekNumber(date);
      const weekKey = `${year}-W${weekNum.toString().padStart(2, "0")}`;

      const dayOfWeek = date.toLocaleString("default", { weekday: "short" });
      const dateStr = date.toISOString().split("T")[0];

      if (!months[monthKey]) {
        months[monthKey] = {
          count: 0,
          year,
          monthName,
          startDate: dateStr,
          endDate: dateStr,
        };
      }
      months[monthKey].count += 1;
      months[monthKey].endDate = dateStr;

      if (!weeks[weekKey]) {
        weeks[weekKey] = {
          count: 0,
          year,
          weekNum,
          monthName,
          startDate: dateStr,
          endDate: dateStr,
          dates: [],
        };
      }
      weeks[weekKey].count += 1;
      weeks[weekKey].dates.push(dateStr);
      weeks[weekKey].endDate = dateStr;

      dates[i] = {
        full: dateStr,
        day: date.getDate(),
        dayOfWeek,
        month: monthName,
        monthKey,
        year,
        week: weekKey,
      };
    }

    return { dates, months, weeks };
  }, [todayDate, getWeekNumber]);

  const { dates, months, weeks } = useMemo(
    () => generateDates(),
    [generateDates],
  );

  const todayPeriodKey = useMemo(() => {
    if (timeScale === "week") {
      const weekNum = getWeekNumber(todayDate);
      const year = todayDate.getFullYear();

      return `${year}-W${weekNum.toString().padStart(2, "0")}`;
    } else {
      const monthName = todayDate.toLocaleString("default", { month: "short" });
      const year = todayDate.getFullYear();

      return `${monthName}-${year}`;
    }
  }, [timeScale, today, todayDate, getWeekNumber]);

  const handleWeekClick = useCallback(() => {
    setTimeScale("week");
    redrawRef.current = true;
  }, []);

  const handleMonthClick = useCallback(() => {
    setTimeScale("month");
    redrawRef.current = true;
  }, []);

  const timePeriods = useMemo((): TimePeriod[] => {
    switch (timeScale) {
      case "month":
        return Object.entries(months).map(
          ([monthKey, { monthName, startDate, endDate }]) => ({
            key: monthKey,
            label: monthName,
            width: 120,
            isCurrentMonth: monthKey === todayPeriodKey,
            startDate,
            endDate,
          }),
        );

      case "week":
        return Object.entries(weeks).map(
          ([weekKey, { weekNum, startDate, endDate }]) => ({
            key: weekKey,
            label: `${weekNum}`,
            tooltip: `Week ${weekNum}`,
            width: 60,
            isCurrentWeek: weekKey === todayPeriodKey,
            startDate,
            endDate,
          }),
        );

      default:
        return dates.map(({ full, day, dayOfWeek, monthKey }) => ({
          key: full,
          label: `${day}`,
          tooltip: `${dayOfWeek} ${day}`,
          width: 32,
          isCurrentDay: full === today,
          monthKey,
        }));
    }
  }, [timeScale, months, weeks, dates, today, todayPeriodKey]);

  const getPhaseColor = useCallback((phase: string) => {
    switch (phase) {
      case "START":
        return { bg: "rgba(59, 130, 246, 0.2)", border: "rgb(59, 130, 246)" };
      case "COLLECTION":
        return { bg: "rgba(16, 185, 129, 0.2)", border: "rgb(16, 185, 129)" };
      case "MIGRATION":
        return { bg: "rgba(245, 158, 11, 0.2)", border: "rgb(245, 158, 11)" };
      case "TEST":
        return { bg: "rgba(139, 92, 246, 0.2)", border: "rgb(139, 92, 246)" };
      case "GO LIVE":
        return { bg: "rgba(236, 72, 153, 0.2)", border: "rgb(236, 72, 153)" };
      default:
        return { bg: "rgba(156, 163, 175, 0.2)", border: "rgb(156, 163, 175)" };
    }
  }, []);

  const todayPositionInPeriod = useMemo(() => {
    const periodDates =
      timeScale === "week"
        ? dates.filter((d) => d.week === todayPeriodKey)
        : dates.filter((d) => d.monthKey === todayPeriodKey);

    if (!periodDates.length) return 0;

    const todayIndex = periodDates.findIndex((date) => date.full === today);

    return todayIndex >= 0 ? todayIndex / (periodDates.length - 1) : 0;
  }, [timeScale, dates, today, todayPeriodKey]);

  const drawHeader = useCallback(() => {
    const canvas = headerCanvasRef.current;

    if (!canvas) return;

    const ctx = canvas.getContext("2d", { alpha: false });

    if (!ctx) return;

    const totalWidth = timePeriods.reduce(
      (sum, period) => sum + period.width,
      0,
    );

    canvas.width = totalWidth;
    canvas.height = 40;

    ctx.fillStyle = "#FFFFFF";
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    let currentX = 0;

    timePeriods.forEach((period) => {
      if (period.isCurrentWeek || period.isCurrentMonth) {
        ctx.fillStyle = "#FEF2F2";
        ctx.fillRect(currentX, 0, period.width, 40);

        if (todayPositionInPeriod > 0) {
          const todayX = currentX + period.width * todayPositionInPeriod;

          ctx.strokeStyle = "#EF4444";
          ctx.beginPath();
          ctx.moveTo(todayX, 0);
          ctx.lineTo(todayX, 40);
          ctx.stroke();
        }
      }

      ctx.strokeStyle = "#E5E7EB";
      ctx.beginPath();
      ctx.moveTo(currentX + period.width, 0);
      ctx.lineTo(currentX + period.width, 40);
      ctx.stroke();

      ctx.fillStyle = "#374151";
      ctx.font = "12px sans-serif";
      ctx.textAlign = "center";
      ctx.textBaseline = "middle";

      if (timeScale === "month") {
        // Month + year
        ctx.fillText(
          period.label + ` ${period.key.split("-")[1]}`,
          currentX + period.width / 2,
          15,
        );
      } else {
        ctx.fillText(period.label, currentX + period.width / 2, 15);
      }

      if (timeScale === "week" && period.startDate && period.endDate) {
        ctx.font = "8px sans-serif";
        ctx.fillText(
          `${formatDateDDMM(period.startDate)} - ${formatDateDDMM(
            period.endDate,
          )}`,
          currentX + period.width / 2,
          30,
        );
      }

      currentX += period.width;
    });

    ctx.strokeStyle = "#E5E7EB";
    ctx.beginPath();
    ctx.moveTo(0, 40);
    ctx.lineTo(canvas.width, 40);
    ctx.stroke();
  }, [timeScale, timePeriods, formatDateDDMM, todayPositionInPeriod, today]);

  const drawGantt = useCallback(() => {
    const canvas = canvasRef.current;

    if (!canvas) return;

    const ctx = canvas.getContext("2d", { alpha: false });

    if (!ctx) return;

    const totalWidth = timePeriods.reduce(
      (sum, period) => sum + period.width,
      0,
    );

    // Get only the current page's projects
    const visibleProjects = filteredProjects.slice(
      (page - 1) * rowCount,
      page * rowCount,
    );

    canvas.width = totalWidth;
    canvas.height = visibleProjects.length * 56;

    ctx.fillStyle = "#FFFFFF";
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    visibleProjects.forEach((project, projectIndex) => {
      const rowY = projectIndex * 56;

      ctx.strokeStyle = "#E5E7EB";
      ctx.beginPath();
      ctx.moveTo(0, rowY + 56);
      ctx.lineTo(canvas.width, rowY + 56);
      ctx.stroke();

      let periodX = 0;

      timePeriods.forEach((period) => {
        if (period.isCurrentWeek || period.isCurrentMonth) {
          ctx.fillStyle = "#FEF2F2";
          ctx.fillRect(periodX, rowY, period.width, 56);

          const todayX = periodX + period.width * todayPositionInPeriod;

          ctx.strokeStyle = "#EF4444";
          ctx.beginPath();
          ctx.moveTo(todayX, rowY);
          ctx.lineTo(todayX, rowY + 56);
          ctx.stroke();
        }

        ctx.strokeStyle = "#E5E7EB";
        ctx.beginPath();
        ctx.moveTo(periodX + period.width, rowY);
        ctx.lineTo(periodX + period.width, rowY + 56);
        ctx.stroke();

        periodX += period.width;
      });

      // Calculate exact positions based on actual dates
      const projectStart = new Date(project.startDate);
      const projectEnd = new Date(project.endDate);

      let startX = 0;
      let endX = 0;
      let periodStart = 0;

      // Find the start position
      for (let i = 0; i < timePeriods.length; i++) {
        const period = timePeriods[i];
        const periodWidth = period.width;

        if (period.startDate && period.endDate) {
          const periodStartDate = new Date(period.startDate);
          const periodEndDate = new Date(period.endDate);

          // Check if project starts in this period
          if (
            projectStart >= periodStartDate &&
            projectStart <= periodEndDate
          ) {
            // Calculate the relative position within this period
            const totalDays =
              (periodEndDate.getTime() - periodStartDate.getTime()) /
                (1000 * 60 * 60 * 24) +
              1;
            const daysFromStart =
              (projectStart.getTime() - periodStartDate.getTime()) /
              (1000 * 60 * 60 * 24);
            const relativePosition = daysFromStart / totalDays;

            startX = periodStart + periodWidth * relativePosition;
            break;
          }
        }

        periodStart += periodWidth;
      }

      // If start date is before our visible range, set to beginning
      if (startX === 0 && projectStart < new Date(dates[0].full)) {
        startX = 0;
      }

      // Find the end position
      periodStart = 0;
      for (let i = 0; i < timePeriods.length; i++) {
        const period = timePeriods[i];
        const periodWidth = period.width;

        if (period.startDate && period.endDate) {
          const periodStartDate = new Date(period.startDate);
          const periodEndDate = new Date(period.endDate);

          // Check if project ends in this period
          if (projectEnd >= periodStartDate && projectEnd <= periodEndDate) {
            // Calculate the relative position within this period
            const totalDays =
              (periodEndDate.getTime() - periodStartDate.getTime()) /
                (1000 * 60 * 60 * 24) +
              1;
            const daysFromStart =
              (projectEnd.getTime() - periodStartDate.getTime()) /
              (1000 * 60 * 60 * 24);
            const relativePosition = daysFromStart / totalDays;

            endX = periodStart + periodWidth * relativePosition;
            break;
          }
        }

        periodStart += periodWidth;
      }

      // If end date is after our visible range, set to the end
      if (endX === 0 && projectEnd > new Date(dates[dates.length - 1].full)) {
        endX = totalWidth;
      }

      // If we didn't find an end yet but we have a start, project must span beyond visible range
      if (endX === 0 && startX > 0) {
        endX = totalWidth;
      }

      // Draw the project bar
      if (startX > 0 || endX > 0) {
        const barWidth = Math.max(10, endX - startX);
        const phaseColor = getPhaseColor(project.fase);

        ctx.fillStyle = phaseColor.bg;
        // Draw rounded rectangle background
        drawRoundedRect(ctx, startX, rowY + 16, barWidth, 16, 4);
        ctx.fill();

        // Draw rounded rectangle border
        ctx.strokeStyle = phaseColor.border;
        drawRoundedRect(ctx, startX, rowY + 16, barWidth, 16, 4);
        ctx.stroke();

        if (project.total > 0 && project.total < 100) {
          const completionWidth = (barWidth * project.total) / 100;
          const radius = 4; // Match the radius used in drawRoundedRect

          // Draw the completion indicator with rounded corners on the left side only
          ctx.fillStyle = phaseColor.border + "40";
          ctx.beginPath();
          ctx.moveTo(startX + radius, rowY + 16);
          ctx.lineTo(startX + completionWidth, rowY + 16);
          ctx.lineTo(startX + completionWidth, rowY + 16 + 16);
          ctx.lineTo(startX + radius, rowY + 16 + 16);
          ctx.quadraticCurveTo(
            startX,
            rowY + 16 + 16,
            startX,
            rowY + 16 + 16 - radius,
          );
          ctx.lineTo(startX, rowY + 16 + radius);
          ctx.quadraticCurveTo(startX, rowY + 16, startX + radius, rowY + 16);
          ctx.closePath();
          ctx.fill();
        }

        if (project.total === 100) {
          ctx.fillStyle = phaseColor.border;
          ctx.font = "12px sans-serif";
          ctx.textAlign = "center";
          ctx.textBaseline = "middle";
          ctx.fillText("✓", startX + barWidth / 2, rowY + 24);
        }
      }
    });
  }, [
    timeScale,
    timePeriods,
    filteredProjects,
    dates,
    today,
    todayPositionInPeriod,
    getPhaseColor,
    page,
    rowCount,
  ]);

  const applyFiltersAndSort = (
    term: string,
    filters: Record<string, string[]>,
    sort: { column: string; direction: "asc" | "desc" } | null,
    dates: RangeValue<DateValue> | null = dateRange,
  ) => {
    let filtered = projects;

    // Apply search term filter
    if (term) {
      filtered = filtered.filter(
        (project) =>
          project.alias.toLowerCase().includes(term.toLowerCase()) ||
          project.agregador.toLowerCase().includes(term.toLowerCase()) ||
          project.id.toLowerCase().includes(term.toLowerCase()),
      );
    }

    // Apply column filters
    Object.entries(filters).forEach(([column, values]) => {
      if (values.length > 0) {
        filtered = filtered.filter((project) =>
          values.includes(String(project[column as keyof Project])),
        );
      }
    });

    if (sort) {
      filtered = [...filtered].sort((a, b) => {
        const aValue = String(a[sort.column as keyof Project]);
        const bValue = String(b[sort.column as keyof Project]);

        if (sort.direction === "asc") {
          return aValue.localeCompare(bValue);
        } else {
          return bValue.localeCompare(aValue);
        }
      });
    }

    if (dates && dates.start && dates.end) {
      filtered = filtered.filter((project) => {
        const goLiveDate = new Date(project.goLive);

        return (
          goLiveDate >= new Date(dates.start.toString()) &&
          goLiveDate <= new Date(dates.end.toString())
        );
      });
    }

    setFilteredProjects(filtered);
    setPage(1);
  };

  const handleClearFilters = () => {
    setActiveFilters({});
    setSortConfig(null);
    setDateRange(null);
    setSearchTerm("");

    // Apply changes in one go
    setFilteredProjects(projects);
    setPage(1);
  };

  const getUniqueValues = (column: keyof Project) => {
    return Array.from(
      new Set(projects.map((project) => String(project[column]))),
    );
  };
  const handleFilterChange = (column: string, selectedValues: string[]) => {
    const newFilters = {
      ...activeFilters,
      [column]: selectedValues,
    };

    if (selectedValues.length === 0) {
      delete newFilters[column];
    }

    setActiveFilters(newFilters);
    applyFiltersAndSort(searchTerm, newFilters, sortConfig);
  };

  const handleSort = (column: string, direction: "asc" | "desc") => {
    const newSortConfig = { column, direction };

    setSortConfig(newSortConfig);
    applyFiltersAndSort(searchTerm, activeFilters, newSortConfig);
  };

  useEffect(() => {
    if (redrawRef.current) {
      drawHeader();
      drawGantt();
      redrawRef.current = false;
    }
  }, [drawHeader, drawGantt]);

  useEffect(() => {
    redrawRef.current = true;
  }, [timeScale, filteredProjects, timePeriods, page]);

  useEffect(() => {
    if (scrollContainerRef.current && canvasRef.current) {
      setTimeout(() => {
        let todayPosition = 0;
        let currentX = 0;

        for (let i = 0; i < timePeriods.length; i++) {
          const period = timePeriods[i];

          if (period.key === todayPeriodKey) {
            todayPosition = currentX + period.width * todayPositionInPeriod;
            break;
          }
          currentX += period.width;
        }

        const containerWidth = scrollContainerRef.current?.clientWidth || 0;

        if (scrollContainerRef.current) {
          scrollContainerRef.current.scrollLeft =
            todayPosition - containerWidth / 2;
        }
      }, 100);
    }
  }, [timeScale, todayPeriodKey, timePeriods, todayPositionInPeriod]);

  return (
    <div className="w-full pt-4">
      <Card className="w-full p-2 mb-4 pb-4" radius={"sm"}>
        <div className="flex flex-col sm:flex-row gap-4 justify-between items-center mb-4">
          <div className="flex gap-2 w-full sm:w-auto">
            <Input
              className="w-full sm:w-96"
              placeholder="Buscar por LID, ALIAS o IMPLEMENTADOR... (◕‿◕✿)"
              startContent={"🔍"}
              value={searchTerm}
              onValueChange={handleSearch}
            />
          </div>
          <div className="flex gap-2 w-full sm:w-auto justify-end">
            <DateRangePicker
              showMonthAndYearPickers
              className="w-min"
              visibleMonths={2}
            />

            <Button
              color={
                Object.keys(activeFilters).length > 0 || dateRange || searchTerm
                  ? "primary"
                  : "default"
              }
              variant="flat"
              onPress={handleClearFilters}
            >
              Borrar filtros{" "}
              {(Object.keys(activeFilters).length > 0 ||
                dateRange ||
                searchTerm) &&
                `(${
                  Object.keys(activeFilters).length +
                  (dateRange ? 1 : 0) +
                  (searchTerm ? 1 : 0)
                })`}
            </Button>
          </div>
        </div>
        <div className={"w-full flex flex-col items-end"}>
          <div className="flex flex-col sm:flex-row gap-4 items-end">
            <ButtonGroup variant="flat">
              <Button
                color={timeScale === "week" ? "primary" : "default"}
                size="sm"
                onPress={handleWeekClick}
              >
                <Icon className="w-4 h-4 mr-1" icon="lucide:calendar-range" />
                Semanas
              </Button>
              <Button
                color={timeScale === "month" ? "primary" : "default"}
                size="sm"
                onPress={handleMonthClick}
              >
                <Icon className="w-4 h-4 mr-1" icon="lucide:calendar" />
                Meses
              </Button>
            </ButtonGroup>
          </div>
        </div>
        <div className={"w-full flex flex-col items-center"}>
          <div className="w-7/12 flex flex-col sm:flex-row gap-4 justify-between items-center">
            <PhaseCountCard phase="START" projects={[]} />
            <PhaseCountCard phase="COLLECTION" projects={[]} />
            <PhaseCountCard phase="MIGRATION" projects={[]} />
            <PhaseCountCard phase="TEST" projects={[]} />
            <PhaseCountCard phase="GO LIVE" projects={[]} />
            <PhaseCountCard phase="INCUBADORA" projects={[]} />
            <PhaseCountCard phase="TOTAL" projects={[]} />
          </div>
        </div>
      </Card>

      <div className="flex w-full">
        <div className="border-r">
          <Table
            key={theme}
            removeWrapper
            aria-label="Project information"
            bottomContent={
              <div className="flex w-full justify-center">
                <Pagination
                  isCompact
                  showControls
                  showShadow
                  color="primary"
                  page={page}
                  total={pages}
                  onChange={(page) => setPage(page)}
                />
              </div>
            }
            className="min-w-full"
          >
            <TableHeader>
              <TableColumn>
                <FilterDropdown
                  activeFilters={activeFilters}
                  column={"id"}
                  items={getUniqueValues("id")}
                  sortConfig={sortConfig}
                  title={"LID"}
                  onFilter={handleFilterChange}
                  onSort={handleSort}
                />
              </TableColumn>
              <TableColumn>
                <FilterDropdown
                  activeFilters={activeFilters}
                  column={"alias"}
                  items={getUniqueValues("alias")}
                  sortConfig={sortConfig}
                  title={"ALIAS"}
                  onFilter={handleFilterChange}
                  onSort={handleSort}
                />
              </TableColumn>
              <TableColumn>
                <FilterDropdown
                  activeFilters={activeFilters}
                  column={"implementacion"}
                  items={getUniqueValues("implementacion")}
                  sortConfig={sortConfig}
                  title={"IMPL. 1"}
                  onFilter={handleFilterChange}
                  onSort={handleSort}
                />
              </TableColumn>
              <TableColumn>
                <FilterDropdown
                  activeFilters={activeFilters}
                  column={"implementacion2"}
                  items={getUniqueValues("implementacion2")}
                  sortConfig={sortConfig}
                  title={"IMPL. 2"}
                  onFilter={handleFilterChange}
                  onSort={handleSort}
                />
              </TableColumn>
              <TableColumn>
                <FilterDropdown
                  activeFilters={activeFilters}
                  column={"estado"}
                  items={getUniqueValues("estado")}
                  sortConfig={sortConfig}
                  title={"ESTADO"}
                  onFilter={handleFilterChange}
                  onSort={handleSort}
                />
              </TableColumn>
              <TableColumn>
                <FilterDropdown
                  activeFilters={activeFilters}
                  column={"total"}
                  items={getUniqueValues("total")}
                  sortConfig={sortConfig}
                  title={"TOTAL"}
                  onFilter={handleFilterChange}
                  onSort={handleSort}
                />
              </TableColumn>
            </TableHeader>
            <TableBody>
              {filteredProjects
                .slice((page - 1) * rowCount, page * rowCount)
                .map((project) => (
                  <TableRow key={project.id}>
                    <TableCell>{project.id}</TableCell>
                    <TableCell>
                      <Tooltip
                        content={
                          <div className="flex flex-col items-center">
                            <h3 className="text-sm font-medium text-default-700">
                              {project.alias}
                            </h3>
                          </div>
                        }
                      >
                        <span className="whitespace-nowrap overflow-hidden text-ellipsis block max-w-[150px]">
                          {project.alias}
                        </span>
                      </Tooltip>
                    </TableCell>
                    <TableCell>
                      <Tooltip
                        content={
                          <div className="flex flex-col items-center">
                            <h3 className="text-sm font-medium text-default-700">
                              {project.implementacion}
                            </h3>
                          </div>
                        }
                      >
                        {UserAvatar(project.implementacion, theme)}
                      </Tooltip>
                    </TableCell>
                    <TableCell>
                      <Tooltip
                        content={
                          <div className="flex flex-col items-center">
                            <h3 className="text-sm font-medium text-default-700">
                              {project.implementacion2 || " "}
                            </h3>
                          </div>
                        }
                      >
                        {project.implementacion2 &&
                          UserAvatar(project.implementacion2 || " ", theme)}
                      </Tooltip>
                    </TableCell>
                    <TableCell
                      className={`whitespace-nowrap overflow-hidden text-ellipsis cursor-pointer${
                        project.estado === "Prevista"
                          ? "text-black"
                          : project.estado === "En curso"
                            ? "text-green-500"
                            : project.estado === "On hold"
                              ? "text-gray-700"
                              : project.estado === "Cancelado"
                                ? "text-red-500"
                                : ""
                      }`}
                    >
                      <div className="flex justify-center">
                        {project.estado === "Prevista" ? (
                          <Icon icon="lucide:clock" width={18} />
                        ) : project.estado === "En curso" ? (
                          <Icon icon="lucide:play-circle" width={18} />
                        ) : project.estado === "On hold" ? (
                          <Icon icon="lucide:pause-circle" width={18} />
                        ) : project.estado === "Cancelado" ? (
                          <Icon icon="lucide:x-circle" width={18} />
                        ) : (
                          <Icon icon="lucide:circle" width={18} />
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="w-full bg-red">
                      {project.total}%
                    </TableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </div>

        <div className="flex-1 overflow-hidden">
          <ScrollShadow
            ref={scrollContainerRef}
            hideScrollBar
            className="w-full overflow-x-auto"
          >
            <div className="flex flex-col min-w-max">
              <canvas
                ref={headerCanvasRef}
                style={{
                  height: "40px",
                  width:
                    timePeriods.reduce((sum, period) => sum + period.width, 0) +
                    "px",
                }}
              />

              <canvas
                ref={canvasRef}
                style={{
                  height:
                    filteredProjects.slice(
                      (page - 1) * rowCount,
                      page * rowCount,
                    ).length *
                      56 +
                    "px",
                  width:
                    timePeriods.reduce((sum, period) => sum + period.width, 0) +
                    "px",
                }}
              />
            </div>
          </ScrollShadow>
        </div>
      </div>

      <div className="mt-4 text-right text-sm text-default-500">
        (っ˘ω˘ς ) Scroll horizontally to see more dates!
      </div>
    </div>
  );
};

export default ProjectsGantt;
