import React, { useState } from "react";
import {
  Card,
  CardBody,
  Avatar,
  Input,
  Button,
  Divider,
  Spacer,
} from "@heroui/react";

import { CommentList } from "./comment-list";

import { Comment } from "@/types/comment";

interface CommentsSectionProps {
  projectId: string;
  currentPhase: string;
}

export const CommentsSection: React.FC<CommentsSectionProps> = ({
  projectId,
  currentPhase,
}) => {
  const [comments, setComments] = useState<Comment[]>([
    {
      id: "1",
      author: "<PERSON>",
      avatarUrl: "https://img.heroui.chat/image/avatar?w=200&h=200&u=1",
      content: "This is amazing! I love the new features you added.",
      timestamp: new Date(Date.now() - 3600000).toISOString(),
    },
    {
      id: "2",
      author: "<PERSON>",
      avatarUrl: "https://img.heroui.chat/image/avatar?w=200&h=200&u=2",
      content:
        "I have a question about how this works. Can someone explain the process in more detail?",
      timestamp: new Date(Date.now() - 86400000).toISOString(),
    },
    {
      id: "3",
      author: "<PERSON>",
      avatarUrl: "https://img.heroui.chat/image/avatar?w=200&h=200&u=3",
      content:
        "Just tried this out and it works perfectly! Thanks for sharing.",
      timestamp: new Date(Date.now() - 172800000).toISOString(),
    },
  ]);

  const [newComment, setNewComment] = React.useState("");
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  const handleAddComment = () => {
    if (!newComment.trim()) return;

    setIsSubmitting(true);

    // Simulate API call delay
    setTimeout(() => {
      const comment: Comment = {
        id: Date.now().toString(),
        author: "You",
        avatarUrl: "https://img.heroui.chat/image/avatar?w=200&h=200&u=4",
        content: newComment.trim(),
        timestamp: new Date().toISOString(),
      };

      setComments((prevComments) => [comment, ...prevComments]);
      setNewComment("");
      setIsSubmitting(false);
    }, 500);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardBody>
          <div>
            <div className="flex gap-4">
              <Avatar
                className="flex-shrink-0"
                size="md"
                src="/broggo_pfp.png"
              />
              <div className="flex-grow">
                <div className="flex gap-2">
                  <Input
                    fullWidth
                    className="flex-grow"
                    placeholder="Escriba el comentario..."
                    value={newComment}
                    variant="bordered"
                    onValueChange={setNewComment}
                  />
                  <Button
                    color="primary"
                    isDisabled={!newComment.trim()}
                    isLoading={isSubmitting}
                    onPress={handleAddComment}
                  >
                    Publicar
                  </Button>
                </div>
              </div>
            </div>

            <div>
              <Spacer y={4} />
              <Divider className="mb-4" />
              <CommentList comments={comments} />
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};
