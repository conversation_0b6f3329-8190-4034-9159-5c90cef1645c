import {
  ApolloClient,
  InMemory<PERSON>ache,
  ApolloProvider,
  HttpLink,
  ApolloLink,
  from,
} from "@apollo/client";

const httpLink = new HttpLink({
  uri: process.env.NEXT_PUBLIC_GRAPHQL_URI || "http://localhost:8000/graphql/",
});

// Auth link to add the token to the headers
const authLink = new ApolloLink((operation, forward) => {
  // Get the authentication token from local storage if it exists
  const token =
    typeof window !== "undefined" ? localStorage.getItem("accessToken") : null;

  // Add the authorization header to the operation
  operation.setContext(({ headers = {} }) => ({
    headers: {
      ...headers,
      authorization: token ? `Bearer ${token}` : "",
    },
  }));

  return forward(operation);
});

const createApolloClient = () => {
  return new ApolloClient({
    link: from([authLink, httpLink]), // Combine the auth link and http link
    cache: new InMemoryCache(),
    defaultOptions: {
      watchQuery: {
        fetchPolicy: "cache-and-network",
      },
    },
  });
};

// Use this for client components
export const client = createApolloClient();

// Apollo Provider component for wrapping your app
export function ApolloProviderWrapper({
  children,
}: {
  children: React.ReactNode;
}) {
  return <ApolloProvider client={client}>{children}</ApolloProvider>;
}
