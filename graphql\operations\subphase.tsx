"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import {
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from "@heroui/table";
import { Button } from "@heroui/button";
import { Icon } from "@iconify/react";
import {
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Form,
  Input,
  Select,
  SelectItem,
} from "@heroui/react";

import { TemplateData } from "@/types/template";
import { AllSubphasesDocument } from "@/graphql/schemas/generated";
import { useQuery } from "@apollo/client";

interface SubphaseProps {
  isCreating: boolean;
  setIsCreating: (isCreating: boolean) => void;
}

// Define a type for subphases
interface Subphase {
  id: number;
  name: string;
  inUse: number;
  position: number;
}

// Component for the Add Subphase Modal
interface AddSubphaseData {
  name: string;
  phase: string;
}

function SubphaseModal({
  isOpen,
  onClose,
  onConfirm,
}: {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (data: AddSubphaseData) => void;
}) {
  const { loading, error, data } = useQuery(AllSubphasesDocument);

  const [formData, setFormData] = React.useState<AddSubphaseData>({
    name: "",
    phase: "",
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onConfirm(formData);
    setFormData({ name: "", phase: "" });
    onClose();
  };

  useEffect(() => {
    console.log("Data from query:", data);
  }, [data]);

  const phaseOptions = [
    { key: "Start", label: "Start" },
    { key: "Collection", label: "Collection" },
    { key: "Migration", label: "Migration" },
    { key: "Test", label: "Test" },
    { key: "Go Live", label: "Go Live" },
    { key: "Incubadora", label: "Incubadora" },
  ];

  return (
    <Modal backdrop="opaque" isOpen={isOpen} size="2xl" onClose={onClose}>
      <ModalContent>
        {(onClose) => (
          <Form className="w-full" onSubmit={handleSubmit}>
            <ModalHeader className="flex flex-col gap-1">
              <h2 className="text-xl font-semibold flex items-center gap-2">
                Añadir subfase
              </h2>
            </ModalHeader>
            <ModalBody className="w-full gap-4">
              <Input
                isRequired
                className="w-full"
                label="Nombre de la subfase"
                labelPlacement="outside"
                placeholder="Insertar nombre de la subfase"
                startContent={
                  <Icon
                    className="text-default-400 pointer-events-none flex-shrink-0"
                    icon="lucide:text"
                  />
                }
                value={formData.name}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, name: value }))
                }
              />

              <Select
                isRequired
                className="w-full"
                label="Fase"
                labelPlacement="outside"
                placeholder="Seleccionar fase"
                selectedKeys={formData.phase ? [formData.phase] : []}
                onChange={(e) => {
                  setFormData((prev) => ({
                    ...prev,
                    phase: e.target.value,
                  }));
                }}
              >
                {phaseOptions.map((phase) => (
                  <SelectItem key={phase.key}>{phase.label}</SelectItem>
                ))}
              </Select>
            </ModalBody>
            <ModalFooter className="w-full flex justify-between">
              <Button variant="flat" onPress={onClose}>
                Cancelar
              </Button>
              <Button color="primary" type="submit">
                Añadir
              </Button>
            </ModalFooter>
          </Form>
        )}
      </ModalContent>
    </Modal>
  );
}

// Generate dummy subphases for each phase
const generateDummySubphases = (prefix: string, count: number): Subphase[] => {
  return Array.from({ length: count }, (_, i) => ({
    id: i + 1,
    name: `${prefix} Subfase ${i + 1}`,
    inUse: Math.floor(Math.random() * 10), // Random number between 0-9 for inUse
    position: i + 1,
  }));
};

export default function Subphase({ isCreating, setIsCreating }: SubphaseProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedSubphase, setSelectedSubphase] = useState<{
    id: number;
    name: string;
    phase: string;
  }>({
    id: 0,
    name: "",
    phase: "",
  });
  const router = useRouter();

  // Create state for each phase's subphases
  const [startSubphases, setStartSubphases] = useState<Subphase[]>(
    generateDummySubphases("Start", 3),
  );
  const [collectionSubphases, setCollectionSubphases] = useState<Subphase[]>(
    generateDummySubphases("Collection", 4),
  );
  const [migrationSubphases, setMigrationSubphases] = useState<Subphase[]>(
    generateDummySubphases("Migration", 2),
  );
  const [testSubphases, setTestSubphases] = useState<Subphase[]>(
    generateDummySubphases("Test", 5),
  );
  const [goLiveSubphases, setGoLiveSubphases] = useState<Subphase[]>(
    generateDummySubphases("Go Live", 3),
  );
  const [incubadoraSubphases, setIncubadoraSubphases] = useState<Subphase[]>(
    generateDummySubphases("Incubadora", 2),
  );

  const handleConfirm = (templateData: TemplateData) => {
    // router.push(
    //   `/plantilla/crear?title=${encodeURIComponent(templateData.name)}&description=${encodeURIComponent(templateData.description)}&type=${encodeURIComponent(templateData.type)}`,
    // );
  };

  // Function to move a subphase up or down in its list
  const moveSubphase = (
    direction: "up" | "down",
    index: number,
    phase: string,
  ) => {
    if (phase === "Start") {
      const newSubphases = [...startSubphases];

      if (
        (direction === "up" && index > 0) ||
        (direction === "down" && index < newSubphases.length - 1)
      ) {
        const swapIndex = direction === "up" ? index - 1 : index + 1;

        [newSubphases[index], newSubphases[swapIndex]] = [
          newSubphases[swapIndex],
          newSubphases[index],
        ];
        // Update positions
        newSubphases.forEach((subphase, i) => {
          subphase.position = i + 1;
        });
        setStartSubphases(newSubphases);
      }
    } else if (phase === "Collection") {
      // Similar implementation for Collection phase
      const newSubphases = [...collectionSubphases];

      if (
        (direction === "up" && index > 0) ||
        (direction === "down" && index < newSubphases.length - 1)
      ) {
        const swapIndex = direction === "up" ? index - 1 : index + 1;

        [newSubphases[index], newSubphases[swapIndex]] = [
          newSubphases[swapIndex],
          newSubphases[index],
        ];
        newSubphases.forEach((subphase, i) => {
          subphase.position = i + 1;
        });
        setCollectionSubphases(newSubphases);
      }
    } else if (phase === "Migration") {
      // Similar implementation for Migration phase
      const newSubphases = [...migrationSubphases];

      if (
        (direction === "up" && index > 0) ||
        (direction === "down" && index < newSubphases.length - 1)
      ) {
        const swapIndex = direction === "up" ? index - 1 : index + 1;

        [newSubphases[index], newSubphases[swapIndex]] = [
          newSubphases[swapIndex],
          newSubphases[index],
        ];
        newSubphases.forEach((subphase, i) => {
          subphase.position = i + 1;
        });
        setMigrationSubphases(newSubphases);
      }
    } else if (phase === "Test") {
      // Similar implementation for Test phase
      const newSubphases = [...testSubphases];

      if (
        (direction === "up" && index > 0) ||
        (direction === "down" && index < newSubphases.length - 1)
      ) {
        const swapIndex = direction === "up" ? index - 1 : index + 1;

        [newSubphases[index], newSubphases[swapIndex]] = [
          newSubphases[swapIndex],
          newSubphases[index],
        ];
        newSubphases.forEach((subphase, i) => {
          subphase.position = i + 1;
        });
        setTestSubphases(newSubphases);
      }
    } else if (phase === "Go Live") {
      // Similar implementation for Go Live phase
      const newSubphases = [...goLiveSubphases];

      if (
        (direction === "up" && index > 0) ||
        (direction === "down" && index < newSubphases.length - 1)
      ) {
        const swapIndex = direction === "up" ? index - 1 : index + 1;

        [newSubphases[index], newSubphases[swapIndex]] = [
          newSubphases[swapIndex],
          newSubphases[index],
        ];
        newSubphases.forEach((subphase, i) => {
          subphase.position = i + 1;
        });
        setGoLiveSubphases(newSubphases);
      }
    } else if (phase === "Incubadora") {
      // Similar implementation for Incubadora phase
      const newSubphases = [...incubadoraSubphases];

      if (
        (direction === "up" && index > 0) ||
        (direction === "down" && index < newSubphases.length - 1)
      ) {
        const swapIndex = direction === "up" ? index - 1 : index + 1;

        [newSubphases[index], newSubphases[swapIndex]] = [
          newSubphases[swapIndex],
          newSubphases[index],
        ];
        newSubphases.forEach((subphase, i) => {
          subphase.position = i + 1;
        });
        setIncubadoraSubphases(newSubphases);
      }
    }
  };

  const handleEditSubphase = (phase: string, subphase: Subphase) => {
    setSelectedSubphase({
      id: subphase.id,
      name: subphase.name,
      phase: phase,
    });
    setIsEditModalOpen(true);
  };

  const handleDeleteSubphase = (phase: string, subphase: Subphase) => {
    setSelectedSubphase({
      id: subphase.id,
      name: subphase.name,
      phase: phase,
    });
    setIsDeleteModalOpen(true);
  };

  const handleEditConfirm = (data: AddSubphaseData) => {
    const { phase: currentPhase } = selectedSubphase;
    const { phase: newPhase, name } = data;

    // If the phase changes, we need to remove from one list and add to another
    if (currentPhase !== newPhase) {
      // Remove from current phase
      deleteSubphase(currentPhase, selectedSubphase.id);

      // Add to new phase with the updated name
      if (newPhase === "Start") {
        const newSubphase: Subphase = {
          id: startSubphases.length + 1,
          name: name,
          inUse: 0,
          position: startSubphases.length + 1,
        };

        setStartSubphases([...startSubphases, newSubphase]);
      } else if (newPhase === "Collection") {
        const newSubphase: Subphase = {
          id: collectionSubphases.length + 1,
          name: name,
          inUse: 0,
          position: collectionSubphases.length + 1,
        };

        setCollectionSubphases([...collectionSubphases, newSubphase]);
      } else if (newPhase === "Migration") {
        const newSubphase: Subphase = {
          id: migrationSubphases.length + 1,
          name: name,
          inUse: 0,
          position: migrationSubphases.length + 1,
        };

        setMigrationSubphases([...migrationSubphases, newSubphase]);
      } else if (newPhase === "Test") {
        const newSubphase: Subphase = {
          id: testSubphases.length + 1,
          name: name,
          inUse: 0,
          position: testSubphases.length + 1,
        };

        setTestSubphases([...testSubphases, newSubphase]);
      } else if (newPhase === "Go Live") {
        const newSubphase: Subphase = {
          id: goLiveSubphases.length + 1,
          name: name,
          inUse: 0,
          position: goLiveSubphases.length + 1,
        };

        setGoLiveSubphases([...goLiveSubphases, newSubphase]);
      } else if (newPhase === "Incubadora") {
        const newSubphase: Subphase = {
          id: incubadoraSubphases.length + 1,
          name: name,
          inUse: 0,
          position: incubadoraSubphases.length + 1,
        };

        setIncubadoraSubphases([...incubadoraSubphases, newSubphase]);
      }
    } else {
      // Just update the name in the current phase
      if (currentPhase === "Start") {
        const newSubphases = [...startSubphases];
        const subphase = newSubphases.find((s) => s.id === selectedSubphase.id);

        if (subphase) {
          subphase.name = name;
          setStartSubphases(newSubphases);
        }
      } else if (currentPhase === "Collection") {
        const newSubphases = [...collectionSubphases];
        const subphase = newSubphases.find((s) => s.id === selectedSubphase.id);

        if (subphase) {
          subphase.name = name;
          setCollectionSubphases(newSubphases);
        }
      } else if (currentPhase === "Migration") {
        const newSubphases = [...migrationSubphases];
        const subphase = newSubphases.find((s) => s.id === selectedSubphase.id);

        if (subphase) {
          subphase.name = name;
          setMigrationSubphases(newSubphases);
        }
      } else if (currentPhase === "Test") {
        const newSubphases = [...testSubphases];
        const subphase = newSubphases.find((s) => s.id === selectedSubphase.id);

        if (subphase) {
          subphase.name = name;
          setTestSubphases(newSubphases);
        }
      } else if (currentPhase === "Go Live") {
        const newSubphases = [...goLiveSubphases];
        const subphase = newSubphases.find((s) => s.id === selectedSubphase.id);

        if (subphase) {
          subphase.name = name;
          setGoLiveSubphases(newSubphases);
        }
      } else if (currentPhase === "Incubadora") {
        const newSubphases = [...incubadoraSubphases];
        const subphase = newSubphases.find((s) => s.id === selectedSubphase.id);

        if (subphase) {
          subphase.name = name;
          setIncubadoraSubphases(newSubphases);
        }
      }
    }
  };

  const handleDeleteConfirm = () => {
    deleteSubphase(selectedSubphase.phase, selectedSubphase.id);
    setIsDeleteModalOpen(false);
  };

  // Function to add a new subphase to a specific phase
  const addSubphase = (phase: string) => {
    if (phase === "Start") {
      const newSubphase: Subphase = {
        id: startSubphases.length + 1,
        name: `Start Subfase ${startSubphases.length + 1}`,
        inUse: 0,
        position: startSubphases.length + 1,
      };

      setStartSubphases([...startSubphases, newSubphase]);
    } else if (phase === "Collection") {
      const newSubphase: Subphase = {
        id: collectionSubphases.length + 1,
        name: `Collection Subfase ${collectionSubphases.length + 1}`,
        inUse: 0,
        position: collectionSubphases.length + 1,
      };

      setCollectionSubphases([...collectionSubphases, newSubphase]);
    } else if (phase === "Migration") {
      const newSubphase: Subphase = {
        id: migrationSubphases.length + 1,
        name: `Migration Subfase ${migrationSubphases.length + 1}`,
        inUse: 0,
        position: migrationSubphases.length + 1,
      };

      setMigrationSubphases([...migrationSubphases, newSubphase]);
    } else if (phase === "Test") {
      const newSubphase: Subphase = {
        id: testSubphases.length + 1,
        name: `Test Subfase ${testSubphases.length + 1}`,
        inUse: 0,
        position: testSubphases.length + 1,
      };

      setTestSubphases([...testSubphases, newSubphase]);
    } else if (phase === "Go Live") {
      const newSubphase: Subphase = {
        id: goLiveSubphases.length + 1,
        name: `Go Live Subfase ${goLiveSubphases.length + 1}`,
        inUse: 0,
        position: goLiveSubphases.length + 1,
      };

      setGoLiveSubphases([...goLiveSubphases, newSubphase]);
    } else if (phase === "Incubadora") {
      const newSubphase: Subphase = {
        id: incubadoraSubphases.length + 1,
        name: `Incubadora Subfase ${incubadoraSubphases.length + 1}`,
        inUse: 0,
        position: incubadoraSubphases.length + 1,
      };

      setIncubadoraSubphases([...incubadoraSubphases, newSubphase]);
    }
  };

  // Function to delete a subphase from a specific phase
  const deleteSubphase = (phase: string, id: number) => {
    if (phase === "Start") {
      const newSubphases = startSubphases.filter(
        (subphase) => subphase.id !== id,
      );

      newSubphases.forEach((subphase, i) => {
        subphase.position = i + 1;
      });
      setStartSubphases(newSubphases);
    } else if (phase === "Collection") {
      const newSubphases = collectionSubphases.filter(
        (subphase) => subphase.id !== id,
      );

      newSubphases.forEach((subphase, i) => {
        subphase.position = i + 1;
      });
      setCollectionSubphases(newSubphases);
    } else if (phase === "Migration") {
      const newSubphases = migrationSubphases.filter(
        (subphase) => subphase.id !== id,
      );

      newSubphases.forEach((subphase, i) => {
        subphase.position = i + 1;
      });
      setMigrationSubphases(newSubphases);
    } else if (phase === "Test") {
      const newSubphases = testSubphases.filter(
        (subphase) => subphase.id !== id,
      );

      newSubphases.forEach((subphase, i) => {
        subphase.position = i + 1;
      });
      setTestSubphases(newSubphases);
    } else if (phase === "Go Live") {
      const newSubphases = goLiveSubphases.filter(
        (subphase) => subphase.id !== id,
      );

      newSubphases.forEach((subphase, i) => {
        subphase.position = i + 1;
      });
      setGoLiveSubphases(newSubphases);
    } else if (phase === "Incubadora") {
      const newSubphases = incubadoraSubphases.filter(
        (subphase) => subphase.id !== id,
      );

      newSubphases.forEach((subphase, i) => {
        subphase.position = i + 1;
      });
      setIncubadoraSubphases(newSubphases);
    }
  };

  // Function to render a subphase table for a specific phase
  const renderSubphaseTable = (phase: string, subphases: Subphase[]) => {
    return (
      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <h2 className="text-2xl font-semibold">{phase}</h2>
        </div>
        <div>
          <Table removeWrapper aria-label={`${phase} subphases table`}>
            <TableHeader>
              <TableColumn>#</TableColumn>
              <TableColumn>Subfase</TableColumn>
              <TableColumn>Campos asociados</TableColumn>
              <TableColumn className="text-center">Acciones</TableColumn>
            </TableHeader>
            <TableBody>
              {subphases.map((subphase, index) => (
                <TableRow key={subphase.id}>
                  <TableCell>{subphase.position}</TableCell>
                  <TableCell>{subphase.name}</TableCell>
                  <TableCell>{subphase.inUse}</TableCell>
                  <TableCell>
                    <div className="flex justify-center gap-2">
                      <Button
                        isIconOnly
                        isDisabled={index === 0}
                        size="sm"
                        variant="light"
                        onPress={() => moveSubphase("up", index, phase)}
                      >
                        <Icon className="text-lg" icon="lucide:chevron-up" />
                      </Button>
                      <Button
                        isIconOnly
                        isDisabled={index === subphases.length - 1}
                        size="sm"
                        variant="light"
                        onPress={() => moveSubphase("down", index, phase)}
                      >
                        <Icon className="text-lg" icon="lucide:chevron-down" />
                      </Button>
                      <Button
                        isIconOnly
                        color="primary"
                        size="sm"
                        variant="flat"
                        onPress={() => handleEditSubphase(phase, subphase)}
                      >
                        <Icon className="text-lg" icon="lucide:edit-3" />
                      </Button>
                      <Button
                        isIconOnly
                        color="danger"
                        isDisabled={subphase.inUse > 0}
                        size="sm"
                        variant="flat"
                        onPress={() => handleDeleteSubphase(phase, subphase)}
                      >
                        <Icon className="text-lg" icon="lucide:trash-2" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    );
  };

  useEffect(() => {
    if (isCreating) {
      setIsOpen(true);
      setIsCreating(false);
    }
  }, [isCreating, setIsCreating]);

  const handleModalConfirm = (data: AddSubphaseData) => {
    // Add subphase with the name from the form
    if (data.phase === "Start") {
      const newSubphase: Subphase = {
        id: startSubphases.length + 1,
        name: data.name,
        inUse: 0,
        position: startSubphases.length + 1,
      };

      setStartSubphases([...startSubphases, newSubphase]);
    } else if (data.phase === "Collection") {
      const newSubphase: Subphase = {
        id: collectionSubphases.length + 1,
        name: data.name,
        inUse: 0,
        position: collectionSubphases.length + 1,
      };

      setCollectionSubphases([...collectionSubphases, newSubphase]);
    } else if (data.phase === "Migration") {
      const newSubphase: Subphase = {
        id: migrationSubphases.length + 1,
        name: data.name,
        inUse: 0,
        position: migrationSubphases.length + 1,
      };

      setMigrationSubphases([...migrationSubphases, newSubphase]);
    } else if (data.phase === "Test") {
      const newSubphase: Subphase = {
        id: testSubphases.length + 1,
        name: data.name,
        inUse: 0,
        position: testSubphases.length + 1,
      };

      setTestSubphases([...testSubphases, newSubphase]);
    } else if (data.phase === "Go Live") {
      const newSubphase: Subphase = {
        id: goLiveSubphases.length + 1,
        name: data.name,
        inUse: 0,
        position: goLiveSubphases.length + 1,
      };

      setGoLiveSubphases([...goLiveSubphases, newSubphase]);
    } else if (data.phase === "Incubadora") {
      const newSubphase: Subphase = {
        id: incubadoraSubphases.length + 1,
        name: data.name,
        inUse: 0,
        position: incubadoraSubphases.length + 1,
      };

      setIncubadoraSubphases([...incubadoraSubphases, newSubphase]);
    }
  };

  return (
    <div className="pt-4 w-full">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {renderSubphaseTable("Start", startSubphases)}
        {renderSubphaseTable("Collection", collectionSubphases)}
        {renderSubphaseTable("Migration", migrationSubphases)}
        {renderSubphaseTable("Test", testSubphases)}
        {renderSubphaseTable("Go Live", goLiveSubphases)}
        {renderSubphaseTable("Incubadora", incubadoraSubphases)}
      </div>
      <SubphaseModal
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        onConfirm={handleModalConfirm}
      />
      <EditModal
        initialData={{
          name: selectedSubphase.name,
          phase: selectedSubphase.phase,
        }}
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        onConfirm={handleEditConfirm}
      />
      <DeleteModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={handleDeleteConfirm}
      />
    </div>
  );
}

function DeleteModal({
  isOpen,
  onClose,
  onConfirm,
}: {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
}) {
  return (
    <Modal backdrop="opaque" isOpen={isOpen} size="2xl" onClose={onClose}>
      <ModalContent>
        {(onClose) => (
          <div className="w-full">
            <ModalHeader className="flex flex-col gap-1">
              <h2 className="text-xl font-semibold flex items-center gap-2">
                Eliminar subfase
              </h2>
            </ModalHeader>
            <ModalBody className="w-full gap-4">
              <p>¿Estás seguro de que deseas eliminar esta subfase?</p>
            </ModalBody>
            <ModalFooter className="w-full flex justify-between">
              <Button variant="flat" onPress={onClose}>
                Cancelar
              </Button>
              <Button color="danger" onPress={onConfirm}>
                Eliminar
              </Button>
            </ModalFooter>
          </div>
        )}
      </ModalContent>
    </Modal>
  );
}
function EditModal({
  isOpen,
  onClose,
  onConfirm,
  initialData,
}: {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (data: AddSubphaseData) => void;
  initialData: AddSubphaseData;
}) {
  const [formData, setFormData] = React.useState<AddSubphaseData>(initialData);

  // Reset form data when initialData changes or modal opens
  useEffect(() => {
    if (isOpen) {
      setFormData(initialData);
    }
  }, [initialData, isOpen]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onConfirm(formData);
    onClose();
  };

  const phaseOptions = [
    "Start",
    "Collection",
    "Migration",
    "Test",
    "Go Live",
    "Incubadora",
  ];

  return (
    <Modal backdrop="opaque" isOpen={isOpen} size="2xl" onClose={onClose}>
      <ModalContent>
        {(onClose) => (
          <Form className="w-full" onSubmit={handleSubmit}>
            <ModalHeader className="flex flex-col gap-1">
              <h2 className="text-xl font-semibold flex items-center gap-2">
                Editar subfase
              </h2>
            </ModalHeader>
            <ModalBody className="w-full gap-4">
              <Input
                isRequired
                className="w-full"
                label="Nombre de la subfase"
                labelPlacement="outside"
                placeholder="Insertar nombre de la subfase"
                startContent={
                  <Icon
                    className="text-default-400 pointer-events-none flex-shrink-0"
                    icon="lucide:text"
                  />
                }
                value={formData.name}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, name: value }))
                }
              />

              <Select
                isRequired
                className="w-full"
                label="Fase"
                labelPlacement="outside"
                placeholder="Seleccionar fase"
                selectedKeys={formData.phase ? [formData.phase] : []}
                onChange={(e) => {
                  setFormData((prev) => ({
                    ...prev,
                    phase: e.target.value,
                  }));
                }}
              >
                {phaseOptions.map((phase) => (
                  <SelectItem key={phase}>{phase}</SelectItem>
                ))}
              </Select>
            </ModalBody>
            <ModalFooter className="w-full flex justify-between">
              <Button variant="flat" onPress={onClose}>
                Cancelar
              </Button>
              <Button color="primary" type="submit">
                Guardar
              </Button>
            </ModalFooter>
          </Form>
        )}
      </ModalContent>
    </Modal>
  );
}
