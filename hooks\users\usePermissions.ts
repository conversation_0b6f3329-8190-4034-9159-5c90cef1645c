"use client";

import { useState } from "react";

import { Permission } from "./useUsers";

import { API_ROUTES } from "@/lib/api";
import { axiosInstance } from "@/lib/axios";

export function usePermissions() {
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchPermissions = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axiosInstance.get(API_ROUTES.ALL_PERMISSIONS);

      if (response.status === 200) {
        setPermissions(response.data);
      } else {
        setError("Failed to fetch permissions");
      }
    } catch (err: any) {
      setError(err.message || "Failed to fetch permissions");
    } finally {
      setLoading(false);
    }
  };

  return {
    permissions,
    loading,
    error,
    fetchPermissions,
  };
}
