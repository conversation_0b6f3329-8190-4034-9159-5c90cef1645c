import type { Role } from "@/types/role";

import React from "react";
import { <PERSON><PERSON> } from "@heroui/react";
import { Icon } from "@iconify/react";

interface RoleActionsProps {
  role: Role;
  onEdit: (role: Role) => void;
  onDelete: (role: Role) => void;
}

export function RoleActions({ role, onEdit, onDelete }: RoleActionsProps) {
  return (
    <div className="flex gap-2 justify-end">
      <Button
        isIconOnly
        color="primary"
        size="sm"
        startContent={<Icon className="text-lg" icon="lucide:edit" />}
        variant="flat"
        onPress={() => onEdit(role)}
      />
      <Button
        isIconOnly
        color="danger"
        size="sm"
        startContent={<Icon className="text-lg" icon="lucide:trash" />}
        variant="flat"
        onPress={() => onDelete(role)}
      />
    </div>
  );
}
