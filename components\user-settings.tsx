import React from "react";
import {
  Modal,
  ModalContent,
  ModalBody,
  Input,
  Tabs,
  Tab,
  Select,
  SelectItem,
  Switch,
} from "@heroui/react";
import { Icon } from "@iconify/react";

import { CursorPetType, useCursorPetStore } from "@/store/use-cursor-pet-store";
import { useRowCountStore } from "@/store/use-row-count-store";
import { useStarsbgStore } from "@/store/use-starsbg-store";
import { User } from "@/types/user";

interface UserSettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: User | null;
}

export const UserSettings = ({
  isOpen,
  onClose,
  user,
}: UserSettingsModalProps) => {
  const { cursorPet, setCursorPet } = useCursorPetStore();
  const { rowCount, setRowCount } = useRowCountStore();
  const { starsEnabled, setStarsEnabled } = useStarsbgStore();

  const handleSave = () => {
    onClose();
  };

  const cursorPetOptions: { key: CursorPetType; label: string }[] = [
    { key: "none", label: "No Pet" },
    { key: "froggo", label: "Froggo" },
    { key: "duck", label: "Duck" },
  ];

  return (
    <Modal
      isOpen={isOpen}
      scrollBehavior="inside"
      size="2xl"
      onOpenChange={onClose}
    >
      <ModalContent>
        {(onClose) => (
          <>
            <ModalBody className="flex flex-col mt-2">
              <Tabs aria-label="Settings" size="lg">
                <Tab
                  key="security"
                  title={
                    <div className="flex items-center gap-2">
                      <Icon icon="lucide:settings" />
                      <span>Ajustes</span>
                    </div>
                  }
                >
                  <div className="flex flex-col gap-4">
                    <Input
                      label="Número de filas"
                      max={100}
                      min={1}
                      placeholder="Inserta el número de filas"
                      type="number"
                      value={rowCount.toString()}
                      variant="bordered"
                      onValueChange={(e) => setRowCount(Number(e))}
                    />
                    <Select
                      label="Cursor Pet"
                      placeholder="Select a cursor pet"
                      selectedKeys={[cursorPet]}
                      onChange={(e) =>
                        setCursorPet(e.target.value as CursorPetType)
                      }
                    >
                      {cursorPetOptions.map((pet) => (
                        <SelectItem key={pet.key}>{pet.label}</SelectItem>
                      ))}
                    </Select>

                    <Switch
                      className="flex items-center gap-2"
                      color="secondary"
                      endContent={<span>😣</span>}
                      isSelected={starsEnabled}
                      size="md"
                      startContent={<div>✨</div>}
                      onValueChange={setStarsEnabled}
                    >
                      <span>Fondo animado</span>
                    </Switch>
                  </div>
                </Tab>
                <Tab
                  key="notifications"
                  title={
                    <div className="flex items-center gap-2">
                      <Icon icon="lucide:bell" />
                      <span>Notifications</span>
                    </div>
                  }
                >
                  <div className="py-4">Notification settings content</div>
                </Tab>
              </Tabs>
            </ModalBody>
          </>
        )}
      </ModalContent>
    </Modal>
  );
};
