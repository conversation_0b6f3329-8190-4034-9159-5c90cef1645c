"use client";

import React from "react";

import { ProtectedLayout } from "@/components/auth/ProtectedLayout";

export default function ContactosLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // [{"id":36,"name":"Puede editar la configuración","codename":"editar_config"},{"id":34,"name":"Puede editar proyectos","codename":"editar_proyectos"},{"id":38,"name":"Puede editar usuarios","codename":"editar_usuarios"},{"id":40,"name":"Puede marcar subtareas como completadas","codename":"subtareas_completadas"},{"id":39,"name":"Puede verificar fases","codename":"verificar_fase"},{"id":35,"name":"Puede visualizar la configuración","codename":"visualizar_config"},{"id":33,"name":"Puede visualizar proyectos","codename":"visualizar_proyectos"},{"id":37,"name":"Puede visualizar usuarios","codename":"visualizar_usuarios"}]
  return (
    <ProtectedLayout requiredPermission="visualizar_usuarios">
      <section className="flex flex-col">{children}</section>
    </ProtectedLayout>
  );
}
