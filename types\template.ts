export interface TemplateData {
  name: string;
  description: string;
  type: string;
  id?: string;
}

export interface CreateTemplateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (data: TemplateData) => void;
  initialData?: Partial<TemplateData>;
}

// Template detail interfaces for the API response
export interface TemplateDetailField {
  id: number;
  name: string;
  type: string;
  weight: string;
  subphase: string;
  description: string;
  is_milestone: boolean;
}

export interface TemplateDetailRule {
  id: number;
  name: string;
  action: string;
  condition: string;
  description: string;
  origin_field: string;
  target_field: string;
}

export interface TemplateDetailNotification {
  id: number;
  name: string;
  value: string;
  description: string;
  trigger_field: string;
  trigger_condition: string;
}

export interface TemplateDetailFullDefinition {
  rules: TemplateDetailRule[];
  fields: TemplateDetailField[];
  notifications: TemplateDetailNotification[];
}

export interface TemplateDetailResponse {
  id: number;
  name: string;
  description: string;
  full_definition: TemplateDetailFullDefinition;
  created_at: string;
  updated_at: string;
}
