"use client";

import { useState } from "react";

import { API_ROUTES } from "@/lib/api";
import { axiosInstance } from "@/lib/axios";

export interface ApiTemplate {
  id: number;
  name: string;
  description: string;
  is_active: boolean;
}

export function useTemplates() {
  const [templates, setTemplates] = useState<ApiTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTemplates = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axiosInstance.get(API_ROUTES.ALL_TEMPLATES);

      if (response.status === 200) {
        setTemplates(response.data);
      } else {
        setError("Failed to fetch templates");
      }
    } catch (err: any) {
      setError(err.message || "Failed to fetch templates");
    } finally {
      setLoading(false);
    }
  };

  return {
    templates,
    loading,
    error,
    fetchTemplates,
  };
}
