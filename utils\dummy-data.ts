import { FieldProps } from "@/types/fields";

export interface Subphase {
  id: number;
  order: number;
  title: string;
  phase: string;
  fields: FieldProps[];
}

// Phase constants
export const PHASES = {
  START: "START",
  COLLECTION: "COLLECTION",
  MIGRATION: "MIGRATION",
  TEST: "TEST",
  "GO LIVE": "GO LIVE",
  INCUBADORA: "INCUBADORA",
};

// Dummy subphases data with fields
export const projectSubphases: Subphase[] = [
  {
    id: 1,
    order: 1,
    title: "Recopilación de información",
    phase: PHASES.COLLECTION,
    fields: [
      {
        id: 1,
        title: "Punto contacto cliente",
        description: "Persona que será el interlocutor principal",
        type: "Selecci<PERSON>",
        milestone: false,
        observation: "test",
        options: [
          { key: "contact1", label: "<PERSON>" },
          { key: "contact2", label: "<PERSON>" },
        ],
        value: "contact1",
      },
      {
        id: 2,
        title: "Documentación recibida",
        description: "Verificación de documentación",
        type: "Tarea",
        milestone: true,
        observation: "Pendiente de recibir último documento",
        value: "completado",
      },
      {
        id: 3,
        title: "Reunión inicial",
        description: "Reunión para definir objetivos y expectativas",
        type: "Documento",
        milestone: false,
        observation: "Reunión programada para el 15/01/2025",
        value: "en_progreso",
      },
      {
        id: 4,
        title: "Análisis de documentación",
        description: "Revisión de la documentación recibida",
        type: "Subtarea",
        milestone: false,
        observation: "",
        subtasks: [
          {
            id: 1,
            title: "Revisión técnica",
            description: "Análisis técnico de la documentación",
            observation: "",
            value: "pendiente",
          },
          {
            id: 2,
            title: "Revisión legal",
            description: "Análisis legal de la documentación",
            observation: "",
            value: "",
          },
        ],
      },
    ],
  },
  {
    id: 2,
    order: 2,
    title: "Análisis inicial",
    phase: PHASES.COLLECTION,
    fields: [
      {
        id: 3,
        title: "Resultado análisis",
        description: "Conclusiones del primer análisis",
        type: "Informativo",
        milestone: false,
        observation: "",
        value: "LOl XD",
      },
      {
        id: 4,
        title: "Aprobación para continuar",
        description: "Verificación de viabilidad del proyecto",
        type: "Tarea",
        milestone: true,
        observation: "",
      },
    ],
  },
  {
    id: 3,
    order: 1,
    title: "Definición de alcance",
    phase: PHASES.START,
    fields: [
      {
        id: 5,
        title: "Áreas incluidas",
        description: "Departamentos que estarán dentro del alcance",
        type: "Selección",
        milestone: false,
        observation: "",
        options: [
          { key: "finance", label: "Finanzas" },
          { key: "operations", label: "Operaciones" },
          { key: "it", label: "Tecnología" },
        ],
      },
      {
        id: 6,
        title: "Documento de alcance",
        description: "Documento final con el alcance definido",
        type: "Tarea",
        milestone: true,
        observation: "",
      },
    ],
  },
  {
    id: 4,
    order: 2,
    title: "Planificación",
    phase: PHASES.START,
    fields: [
      {
        id: 7,
        title: "Cronograma",
        description: "Fechas estimadas para cada fase",
        type: "Informativo",
        milestone: false,
        observation: "",
      },
      {
        id: 8,
        title: "Recursos asignados",
        description: "Personal asignado al proyecto",
        type: "Selección",
        milestone: false,
        observation: "",
        options: [
          { key: "team1", label: "Equipo Alpha" },
          { key: "team2", label: "Equipo Beta" },
        ],
      },
    ],
  },
  {
    id: 5,
    order: 1,
    title: "Diseño de mapeo",
    phase: PHASES.MIGRATION,
    fields: [
      {
        id: 9,
        title: "Estructura de datos",
        description: "Definición de la estructura de datos",
        type: "Informativo",
        milestone: false,
        observation: "",
      },
      {
        id: 10,
        title: "Mapeo aprobado",
        description: "Aprobación del diseño de mapeo",
        type: "Tarea",
        milestone: true,
        observation: "",
      },
    ],
  },
  {
    id: 6,
    order: 1,
    title: "Pruebas unitarias",
    phase: PHASES.TEST,
    fields: [
      {
        id: 11,
        title: "Casos de prueba",
        description: "Definición de casos de prueba",
        type: "Informativo",
        milestone: false,
        observation: "",
      },
      {
        id: 12,
        title: "Resultados pruebas",
        description: "Resultados de las pruebas realizadas",
        type: "Tarea",
        milestone: true,
        observation: "",
      },
    ],
  },
  {
    id: 7,
    order: 2,
    title: "Pruebas de integración",
    phase: PHASES.TEST,
    fields: [
      {
        id: 13,
        title: "Escenarios de prueba",
        description: "Escenarios completos de prueba",
        type: "Informativo",
        milestone: false,
        observation: "",
      },
      {
        id: 14,
        title: "Certificación",
        description: "Certificación final de pruebas",
        type: "Tarea",
        milestone: true,
        observation: "",
      },
    ],
  },
  {
    id: 8,
    order: 1,
    title: "Preparación para implementación",
    phase: PHASES.INCUBADORA,
    fields: [
      {
        id: 15,
        title: "Entorno preparado",
        description: "Verificación de entorno de producción",
        type: "Tarea",
        milestone: true,
        observation: "",
      },
      {
        id: 16,
        title: "Checklist pre-implementación",
        description: "Lista de verificación previa",
        type: "Selección",
        milestone: false,
        observation: "",
        options: [
          { key: "check_complete", label: "Verificación completa" },
          { key: "check_partial", label: "Verificación parcial" },
        ],
      },
    ],
  },
  {
    id: 9,
    order: 1,
    title: "Lanzamiento",
    phase: PHASES["GO LIVE"],
    fields: [
      {
        id: 17,
        title: "Confirmación final",
        description: "Confirmación para el lanzamiento",
        type: "Tarea",
        milestone: true,
        observation: "",
      },
      {
        id: 18,
        title: "Lanzamiento completo",
        description: "Verificación de lanzamiento exitoso",
        type: "Informativo",
        milestone: true,
        observation: "",
      },
    ],
  },
  {
    id: 10,
    order: 2,
    title: "Post-implementación",
    phase: PHASES["GO LIVE"],
    fields: [
      {
        id: 19,
        title: "Evaluación inicial",
        description: "Evaluación tras primeras 24 horas",
        type: "Informativo",
        milestone: false,
        observation: "",
      },
      {
        id: 20,
        title: "Plan de soporte",
        description: "Plan de soporte post-implementación",
        type: "Tarea",
        milestone: true,
        observation: "",
      },
    ],
  },
];
