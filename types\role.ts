export interface Permission {
  id: number;
  name: string;
  codename: string;
  content_type: string;
}

export interface Role {
  id: number;
  name: string;
  permissions: Permission[] | number[] | string[];
}

export const PERMISSIONS = [
  "Visualizar proyectos",
  "Gestionar proyectos",
  "Visualizar usuarios",
  "Gestionar usuarios",
  "Visualziar configuracion",
  "Gestionar configuracion",
  "Administrador",
] as const;

export const ROLES: Role[] = [
  {
    id: 1,
    name: "Administrator",
    permissions: [
      "Visualizar proyectos",
      "Gestionar proyectos",
      "Visualizar usuarios",
      "Gestionar usuarios",
      "Visualziar configuracion",
      "Gestionar configuracion",
      "Administrador",
    ],
  },
  {
    id: 2,
    name: "Senior Editor",
    permissions: [
      "Visualizar proyectos",
      "Gestionar proyectos",
      "Visualizar usuarios",
      "Gestionar usuarios",
    ],
  },
  {
    id: 3,
    name: "Content Manager",
    permissions: ["Visualizar proyectos", "Gestionar proyectos"],
  },
  {
    id: 4,
    name: "Analyst",
    permissions: ["Visualizar proyectos"],
  },
  {
    id: 5,
    name: "Guest User",
    permissions: ["Visualizar proyectos"],
  },
];
