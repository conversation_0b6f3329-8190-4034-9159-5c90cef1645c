"use client";
import { useState } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Input,
  Select,
  SelectItem,
  Form,
  Autocomplete,
  AutocompleteItem,
} from "@heroui/react";
import { useQuery } from "@apollo/client";

import { GET_ALL_BASIC_PROJECTS_INFO } from "@/graphql/operations/projects";

interface CreateContactFormProps {
  onClose: () => void;
  onAddContact?: (contact: any) => void;
  projectId?: number | null;
}

const contactTypes = [
  "Empresa",
  "Agregador",
  "Add-on",
  "OOS",
  "Impuestos",
  "SS",
  "GL File",
];

const CreateContactForm = ({
  onClose,
  onAddContact,
  projectId,
}: CreateContactFormProps) => {
  const { data: allProjects } = useQuery(GET_ALL_BASIC_PROJECTS_INFO);

  const [formData, setFormData] = useState({
    type: contactTypes[0],
    name: "",
    email: "",
    position: "",
    projectId: projectId || null,
  });

  const [selectedProjectKey, setSelectedProjectKey] = useState<string | null>(
    projectId ? projectId.toString() : null
  );
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
  ) => {
    const { name, value } = e.target;

    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = () => {
    // Console log the form data when creating a contact
    console.log("Creating contact with data:", formData);
    console.log("Selected project ID:", formData.projectId);
    console.log(
      "Selected project details:",
      formData.projectId
        ? allProjects?.allProjects?.find(
            (p: any) => p.id === formData.projectId,
          )
        : "No project selected",
    );

    // Here you would typically make an API call to save the contact
    // For demo purposes, we'll just close the modal
    if (onAddContact) {
      onAddContact(formData);
    }
    onClose();
  };

  return (
    <>
      <ModalHeader>Crear Nuevo Contacto</ModalHeader>
      <Form
        onSubmit={(e) => {
          e.preventDefault();
          handleSubmit();
        }}
      >
        <ModalBody className="w-full">
          <div className="flex flex-col gap-4 w-full">
            <div className="w-full">
              <label
                className="block text-sm font-medium mb-1"
                htmlFor="contact-type"
              >
                Tipo de contacto
              </label>
              <Select
                className="w-full"
                id="contact-type"
                name="type"
                placeholder="Selecciona un tipo"
                selectionMode="single"
                value={formData.type}
                onChange={handleChange}
              >
                {contactTypes.map((type) => (
                  <SelectItem key={type}>{type}</SelectItem>
                ))}
              </Select>
            </div>
            <div>
              <label
                className="block text-sm font-medium mb-1"
                htmlFor="contact-name"
              >
                Nombre
              </label>
              <Input
                className="w-full"
                id="contact-name"
                name="name"
                placeholder="Nombre completo"
                value={formData.name}
                onChange={handleChange}
              />
            </div>
            <div>
              <label
                className="block text-sm font-medium mb-1"
                htmlFor="contact-email"
              >
                Correo electrónico
              </label>
              <Input
                className="w-full"
                id="contact-email"
                name="email"
                placeholder="<EMAIL>"
                type="email"
                value={formData.email}
                onChange={handleChange}
              />
            </div>
            <div>
              <label
                className="block text-sm font-medium mb-1"
                htmlFor="contact-position"
              >
                Cargo
              </label>
              <Input
                className="w-full"
                id="contact-position"
                name="position"
                placeholder="Cargo o posición"
                value={formData.position}
                onChange={handleChange}
              />
            </div>
          </div>
          <div>
            <label
              className="block text-sm font-medium mb-1"
              htmlFor="project-select"
            >
              Implementación
            </label>
            {projectId ? (
              <Input
                className="w-full"
                id="project-select"
                isDisabled={true}
                name="projectId"
                value={
                  allProjects?.allProjects?.find((p: any) => p.id === projectId)
                    ?.alias || `Proyecto ID: ${projectId}`
                }
              />
            ) : (
              <Autocomplete
                className="w-full"
                id="project-select"
                placeholder="Selecciona una implementación"
                selectedKey={selectedProjectKey}
                variant="bordered"
                onSelectionChange={(key) => {
                  const projectId = key ? Number(key) : null;
                  const selectedProject = projectId
                    ? allProjects?.allProjects?.find(
                        (p: any) => p.id === projectId,
                      )
                    : null;

                  console.log("Project selected:", {
                    key,
                    projectId,
                    selectedProject,
                  });

                  setSelectedProjectKey(key ? String(key) : null);
                  setFormData((prev) => ({
                    ...prev,
                    projectId,
                  }));
                }}
              >
                {allProjects?.allProjects?.map((project: any) => (
                  <AutocompleteItem
                    key={project.id.toString()}
                    textValue={project.alias}
                  >
                    <div className="text-default-700">
                      <div className="font-medium">{project.alias}</div>
                      <div className="text-sm text-default-500">
                        {project.aggregator}
                      </div>
                      <div className="text-xs text-default-400">
                        LID: {project.lid}
                      </div>
                    </div>
                  </AutocompleteItem>
                ))}
              </Autocomplete>
            )}
          </div>
        </ModalBody>
        <ModalFooter className="justify-end w-full">
          <Button
            color="danger"
            type="button"
            variant="light"
            onPress={onClose}
          >
            Cancelar
          </Button>
          <Button color="primary" type="submit">
            Guardar
          </Button>
        </ModalFooter>
      </Form>
    </>
  );
};

export default CreateContactForm;
