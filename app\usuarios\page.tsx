"use client";

import { But<PERSON> } from "@heroui/button";
import { Tab, Tabs } from "@heroui/tabs";
import { Key, useState } from "react";
import { RiFileExcel2Fill } from "react-icons/ri";

import { title } from "@/components/primitives";
import UsersTable from "@/components/users/users-table";
import RolesTable from "@/components/users/roles-table";
import { useUsersList } from "@/hooks/users/useUsersList";

export default function UsuariosPage() {
  const { loading, syncUsersWithCore } = useUsersList();

  const [selectedTab, setSelectedTab] = useState("users");

  const handleTabChange = (key: Key) => {
    setSelectedTab(key.toString());
  };

  return (
    <>
      <div className="flex justify-between items-center w-full h-auto">
        <h2 className={title({ size: "sm" })}>
          {selectedTab === "users" ? "Usuarios" : "Roles"}
        </h2>
        <div className="flex items-center">
          <Tabs
            key="solid"
            aria-label="Tabs variants"
            selectedKey={selectedTab}
            variant="solid"
            onSelectionChange={handleTabChange}
          >
            <Tab key="users" title="Usuarios" />
            <Tab key="roles" title="Roles" />
          </Tabs>
          <Button
            className={"ml-2"}
            color="default"
            isDisabled={selectedTab === "roles" || loading}
            isLoading={loading}
            onPress={async () => {
              await syncUsersWithCore();
            }}
          >
            {loading ? "" : "Sync with core"}
          </Button>
          <Button
            className={"ml-2"}
            color="default"
            // href="/proyecto/crear"
            isDisabled={selectedTab === "roles"}
          >
            Reset all passwords
          </Button>
          <Button
            isIconOnly
            aria-label="Exportar a Excel"
            className={"ml-2"}
            color="primary"
            isDisabled={selectedTab === "roles"}
          >
            <RiFileExcel2Fill size={30} />
          </Button>
        </div>
      </div>
      <div className={"w-full pt-4"}>
        {selectedTab === "users" ? <UsersTable /> : <RolesTable />}
      </div>
    </>
  );
}
