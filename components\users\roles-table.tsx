import React, { useEffect, useState } from "react";
import {
  <PERSON>ton,
  Card,
  Input,
  Pagination,
  Table,
  TableHeader,
  TableRow,
  TableCell,
  TableBody,
  TableColumn,
  SortDescriptor,
  Spinner,
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { useAsyncList } from "@react-stately/data";

import { RoleActions } from "./role-actions";
import { RolePermissions } from "./role-permissions";
import { RoleModal } from "./role-modal";

import { useRoles } from "@/hooks/users/useRoles";
import { type Role, type Permission } from "@/types/role";

export default function RolesTable() {
  const { roles, loading: isLoading, fetchRoles } = useRoles();

  const [page, setPage] = useState(1);
  const rowsPerPage = Number(process.env.ROWS_PER_PAGE) || 10;
  const [searchTerm, setSearchTerm] = useState("");
  const [sortDescriptor, setSortDescriptor] = useState<SortDescriptor>({
    column: "name",
    direction: "ascending",
  });
  const [currentData, setCurrentData] = useState<Role[]>([]);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [modalMode, setModalMode] = useState<"edit" | "delete" | "create">(
    "edit",
  );
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleClearFilters = () => {
    setSearchTerm("");
    const mappedRoles = mapRolesPermissions(roles);

    setCurrentData(mappedRoles);
    list.reload();
  };

  // Helper function to format role permissions for display
  const mapRolesPermissions = (rolesData: Role[]) => {
    return rolesData.map((role) => {
      let permissionNames: string[] = [];

      if (role.permissions && role.permissions.length > 0) {
        // Check permission type and extract names
        if (
          typeof role.permissions[0] === "object" &&
          "name" in role.permissions[0]
        ) {
          permissionNames = (role.permissions as Permission[]).map(
            (p) => p.name,
          );
        } else if (typeof role.permissions[0] === "string") {
          permissionNames = role.permissions as string[];
        }
      }

      return {
        ...role,
        permissions: permissionNames,
      };
    });
  };

  useEffect(() => {
    if (roles && roles.length > 0) {
      const mappedRoles = mapRolesPermissions(roles);

      setCurrentData(mappedRoles);
    }
  }, [roles]);

  const list = useAsyncList<Role>({
    async load() {
      return {
        items: currentData,
      };
    },
    async sort({ items: roleItems, sortDescriptor: roleSortDescriptor }) {
      return {
        items: roleItems.sort((roleA: Role, roleB: Role) => {
          const sortColumn = roleSortDescriptor.column as keyof Role;
          const sortDirection =
            roleSortDescriptor.direction === "ascending" ? 1 : -1;

          return (
            sortDirection *
            (roleA[sortColumn] as string).localeCompare(
              roleB[sortColumn] as string,
            )
          );
        }),
      };
    },
  });

  const startIndex = (page - 1) * rowsPerPage;
  const endIndex = startIndex + rowsPerPage;
  const paginatedItems = list.items.slice(startIndex, endIndex);
  const pages = Math.ceil(list.items.length / rowsPerPage);

  useEffect(() => {
    fetchRoles();
  }, []);

  useEffect(() => {
    if (sortDescriptor.column) {
      list.sort(sortDescriptor as any);
    }
  }, [sortDescriptor]);

  useEffect(() => {
    list.reload();
  }, [currentData]);

  useEffect(() => {
    setPage(1);
    list.reload();
  }, [searchTerm]);

  const handleSearch = (value: string) => {
    setSearchTerm(value);

    if (value) {
      const filteredItems = currentData.filter((role) =>
        role.name.toLowerCase().includes(value.toLowerCase()),
      );

      setCurrentData(filteredItems);
    } else {
      const mappedRoles = mapRolesPermissions(roles);

      setCurrentData(mappedRoles);
    }

    list.reload();
  };

  const handleEdit = (role: Role) => {
    setSelectedRole(role);
    setModalMode("edit");
    setIsModalOpen(true);
  };

  const handleDelete = (role: Role) => {
    setSelectedRole(role);
    setModalMode("delete");
    setIsModalOpen(true);
  };

  const handleModalConfirm = async () => {
    await fetchRoles();
  };

  return (
    <>
      <Card className="p-2 mb-4 w-full pt-4" radius={"sm"}>
        <div className="flex flex-col sm:flex-row gap-4 justify-between items-center mb-4">
          <div className="flex gap-2 w-full sm:w-auto">
            <Input
              className="w-full sm:w-96"
              placeholder="Buscar roles o permisos..."
              startContent={"🔍"}
              value={searchTerm}
              onValueChange={handleSearch}
            />
          </div>
          <div className="flex gap-2 w-full sm:w-auto justify-end">
            <Button
              color="primary"
              startContent={<Icon icon="lucide:plus" />}
              onPress={() => {
                setSelectedRole(null);
                setModalMode("create");
                setIsModalOpen(true);
              }}
            >
              Agregar Rol
            </Button>
          </div>
        </div>
      </Card>

      <Table
        aria-label="Tabla de roles"
        bottomContent={
          <div className="flex w-full justify-center">
            <Pagination
              color="primary"
              isCompact={true}
              page={page}
              showControls={true}
              total={pages}
              onChange={(page) => setPage(page)}
            />
          </div>
        }
        removeWrapper={true}
        sortDescriptor={sortDescriptor as any}
        onSortChange={setSortDescriptor}
      >
        <TableHeader>
          <TableColumn key="name" allowsSorting>
            NOMBRE
          </TableColumn>
          <TableColumn key="permissions">PERMISOS</TableColumn>
          <TableColumn key="actions" align="end">
            ACCIONES
          </TableColumn>
        </TableHeader>
        <TableBody
          emptyContent="No se encontraron roles"
          isLoading={isLoading && paginatedItems.length === 0}
          items={paginatedItems}
          loadingContent={<Spinner label="Cargando roles..." />}
        >
          {(item) => (
            <TableRow key={item.id}>
              <TableCell>{item.name}</TableCell>
              <TableCell>
                <RolePermissions permissions={item.permissions as string[]} />
              </TableCell>
              <TableCell align="right">
                <RoleActions
                  role={item}
                  onDelete={handleDelete}
                  onEdit={handleEdit}
                />
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>

      <RoleModal
        isOpen={isModalOpen}
        mode={modalMode}
        role={selectedRole}
        onClose={() => setIsModalOpen(false)}
        onConfirm={handleModalConfirm}
      />
    </>
  );
}
