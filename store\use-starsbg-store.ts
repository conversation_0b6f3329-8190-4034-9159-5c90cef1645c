import { create } from "zustand";
import { persist } from "zustand/middleware";

interface StarsbgStore {
  starsEnabled: boolean;
  setStarsEnabled: (enabled: boolean) => void;
}

export const useStarsbgStore = create<StarsbgStore>()(
  persist(
    (set) => ({
      starsEnabled: true,
      setStarsEnabled: (enabled) => set({ starsEnabled: enabled }),
    }),
    {
      name: "starsbg-storage",
    },
  ),
);
