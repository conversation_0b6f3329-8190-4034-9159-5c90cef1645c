"use client";

import React, { useEffect, useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Spin<PERSON>,
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { useRouter } from "next/navigation";

import { CreateTemplateModal } from "./create-template-modal";
import { TemplateViewModal } from "./template-view-modal";

import { TemplateData } from "@/types/template";
import { useTemplates } from "@/hooks/templates/useTemplates";

interface Template {
  id: number;
  title: string;
  description: string;
  is_active: boolean;
}

interface TemplatesProps {
  isCreating: boolean;
  setIsCreating: (value: boolean) => void;
  canEditConfiguration: boolean;
}

export default function Templates({
  isCreating,
  setIsCreating,
  canEditConfiguration,
}: TemplatesProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [clonedTemplate, setClonedTemplate] = useState<Template | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const router = useRouter();
  const {
    templates: apiTemplates,
    loading,
    error,
    fetchTemplates,
  } = useTemplates();

  // Transform API data to match component interface
  const templateData: Template[] = apiTemplates.map((template) => ({
    id: template.id,
    title: template.name,
    description: template.description,
    is_active: template.is_active,
  }));

  const handleConfirm = (templateData: TemplateData) => {
    // Build the URL with template data
    const params = new URLSearchParams({
      title: templateData.name,
      description: templateData.description,
    });

    // If type contains a template ID (when cloning), add it as id parameter
    if (templateData.type) {
      params.append("id", templateData.type);
    }

    router.push(`/plantilla/crear?${params.toString()}`);
  };

  const handleCloneTemplate = (template: Template) => {
    setClonedTemplate(template);
    setIsOpen(true);
  };

  const handleViewTemplate = (template: Template) => {
    setSelectedTemplate(template);
    setIsViewModalOpen(true);
  };

  const handleModalClose = () => {
    setIsOpen(false);
    setClonedTemplate(null);
  };

  const handleViewModalClose = () => {
    setIsViewModalOpen(false);
    setSelectedTemplate(null);
  };

  useEffect(() => {
    if (isCreating) {
      setIsOpen(true);
      setIsCreating(false);
    }
  }, [isCreating, setIsCreating]);

  useEffect(() => {
    if (isOpen) return;
    setClonedTemplate(null);
    setIsCreating(false);
  }, [isOpen]);

  useEffect(() => {
    fetchTemplates();
  }, []);

  if (loading) {
    return (
      <div className="pt-4 w-full flex justify-center items-center min-h-[200px]">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="pt-4 w-full flex justify-center items-center min-h-[200px]">
        <div className="text-center">
          <Icon
            className="text-4xl text-danger mb-2"
            icon="lucide:alert-circle"
          />
          <p className="text-danger">Error loading templates: {error}</p>
          <Button
            className="mt-2"
            color="primary"
            variant="flat"
            onPress={fetchTemplates}
          >
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  if (templateData.length === 0) {
    return (
      <div className="pt-4 w-full flex justify-center items-center min-h-[200px]">
        <div className="text-center">
          <Icon
            className="text-4xl text-default-400 mb-2"
            icon="lucide:folder-open"
          />
          <p className="text-default-500">No templates available</p>
        </div>
      </div>
    );
  }

  return (
    <div className="pt-4 w-full">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {templateData
          .filter((template) => template.is_active)
          .map((template) => (
            <Card
              key={template.id}
              className="border border-default-200 cursor-pointer h-[185px] flex flex-col"
            >
              <CardBody className="p-0 flex-grow overflow-hidden">
                <div className="p-4 h-full flex flex-col">
                  <Tooltip content={template.title}>
                    <h3 className="text-large font-medium mb-2 line-clamp-1 overflow-hidden text-ellipsis">
                      {template.title}
                    </h3>
                  </Tooltip>
                  <Tooltip content={template.description}>
                    <p className="text-small text-default-500 line-clamp-3 overflow-hidden">
                      {template.description}
                    </p>
                  </Tooltip>
                </div>
              </CardBody>
              <CardFooter className="flex justify-between items-center px-4 py-3 border-t border-default-200 mt-auto">
                <Button
                  color="primary"
                  isDisabled={!canEditConfiguration}
                  size="sm"
                  startContent={<Icon className="text-lg" icon="lucide:copy" />}
                  variant="flat"
                  onPress={() => handleCloneTemplate(template)}
                >
                  Clonar plantilla
                </Button>
                <Button
                  isIconOnly
                  aria-label="Preview template"
                  size="sm"
                  variant="light"
                  onPress={() => handleViewTemplate(template)}
                >
                  <Icon className="text-lg" icon="lucide:eye" />
                </Button>
              </CardFooter>
            </Card>
          ))}
      </div>

      <CreateTemplateModal
        initialData={
          clonedTemplate
            ? {
                name: clonedTemplate.title,
                description: clonedTemplate.description,
                type: "",
                id: clonedTemplate.id.toString(),
              }
            : undefined
        }
        isOpen={isOpen}
        onClose={handleModalClose}
        onConfirm={handleConfirm}
      />

      <TemplateViewModal
        isOpen={isViewModalOpen}
        template={selectedTemplate}
        onClose={handleViewModalClose}
      />
    </div>
  );
}
