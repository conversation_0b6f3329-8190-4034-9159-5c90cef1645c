"use client";

import { useState } from "react";

import { TemplateDetailResponse } from "@/types/template";
import { API_ROUTES } from "@/lib/api";
import { axiosInstance } from "@/lib/axios";

export function useTemplateDetail() {
  const [templateDetail, setTemplateDetail] = useState<TemplateDetailResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchTemplateDetail = async (id: string) => {
    try {
      setLoading(true);
      setError(null);

      const response = await axiosInstance.get(`${API_ROUTES.TEMPLATE_DETAIL}${id}/`);

      if (response.status === 200) {
        setTemplateDetail(response.data);
        return response.data;
      } else {
        setError("Failed to fetch template detail");
      }
    } catch (err: any) {
      setError(err.message || "Failed to fetch template detail");
    } finally {
      setLoading(false);
    }
  };

  const clearTemplateDetail = () => {
    setTemplateDetail(null);
    setError(null);
  };

  return {
    templateDetail,
    loading,
    error,
    fetchTemplateDetail,
    clearTemplateDetail,
  };
}
