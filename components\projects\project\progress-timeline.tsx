import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from "@heroui/react"; // Updated import path

import { getPhaseStyle, phaseColors } from "@/components/primitives";

const timelineSteps = [
  {
    label: "START",
    dates: ["02/01/2025", "04/01/2025"],
    status: "completed",
    progress: 100,
  },
  {
    label: "COLLECTION",
    dates: ["26/01/2025", "20/03/2025"],
    status: "current",
    progress: 65,
  },
  {
    label: "MIGRATION",
    dates: ["15/02/2025", "15/02/2025"],
    status: "pending",
    progress: 30,
  },
  {
    label: "TEST",
    dates: ["16/03/2025", "16/03/2025"],
    status: "pending",
    progress: 12,
  },
  {
    label: "GO LIVE",
    dates: ["27/04/2025", "27/04/2025"],
    status: "pending",
    progress: 18,
  },
  {
    label: "INCUBADORA",
    dates: ["27/05/2025", "27/05/2025"],
    status: "pending",
    progress: 7,
  },
];

// Helper function to extract color from phase style
const extractPhaseColors = (phase: string, isDarkMode = false) => {
  isDarkMode = !isDarkMode;
  switch (phase.toLowerCase()) {
    case "incubadora":
      return {
        bg: isDarkMode ? phaseColors.incubadora.bg : "#dbeafe",
        text: isDarkMode ? phaseColors.incubadora.text : "#1d4ed8",
        border: isDarkMode ? phaseColors.incubadora.border : "#1d4ed8",
      };
    case "migration":
      return {
        bg: isDarkMode ? phaseColors.migration.bg : "#fef3c7", // yellow bg
        text: isDarkMode ? phaseColors.migration.text : "#ca8a04", // yellow text
        border: isDarkMode ? phaseColors.migration.border : "#ca8a04", // yellow border
      };
    case "test":
      return {
        bg: isDarkMode ? phaseColors.test.bg : "#f0abfc", // purple bg
        text: isDarkMode ? phaseColors.test.text : "#a855f7", // purple text
        border: isDarkMode ? phaseColors.test.border : "#a855f7", // purple border
      };
    case "go live":
      return {
        bg: isDarkMode ? phaseColors["go live"].bg : "#bbf7d0", // green bg
        text: isDarkMode ? phaseColors["go live"].text : "#4ade80", // green text
        border: isDarkMode ? phaseColors["go live"].border : "#4ade80", // green border
      };
    case "collection":
      return {
        bg: isDarkMode ? phaseColors.collection.bg : "#fef9c3", // yellow bg
        text: isDarkMode ? phaseColors.collection.text : "#ca8a04", // yellow text
        border: isDarkMode ? phaseColors.collection.border : "#ca8a04", // yellow border
      };
    case "start":
      return {
        bg: isDarkMode ? phaseColors.start.bg : "#e0f2fe", // blue bg
        text: isDarkMode ? phaseColors.start.text : "#0284c7", // blue text
        border: isDarkMode ? phaseColors.start.border : "#0284c7", // blue border
      };
    case "default":
    default:
      return {
        bg: isDarkMode ? "rgba(17, 24, 39, 0.5)" : "#e5e7eb", // gray bg
        text: isDarkMode ? "#9ca3af" : "#374151", // gray text
        border: isDarkMode ? "#9ca3af" : "#374151", // gray border
      };
  }
};

export interface ProgressTimelineProps {
  onPhaseSelect?: (phase: string) => void;
  selectedPhase?: string;
  isEditMode?: boolean;
}

export const ProgressTimeline: React.FC<ProgressTimelineProps> = ({
  onPhaseSelect,
  selectedPhase,
  isEditMode = false,
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [internalSelectedPhase, setInternalSelectedPhase] = useState<
    string | undefined
  >(
    selectedPhase ||
      timelineSteps.find((step) => step.status === "current")?.label,
  );
  const [temporarySelectedPhase, setTemporarySelectedPhase] = useState<
    string | undefined
  >(
    selectedPhase ||
      timelineSteps.find((step) => step.status === "current")?.label,
  );

  // Use either the controlled or internal state
  const effectiveSelectedPhase =
    selectedPhase !== undefined ? selectedPhase : internalSelectedPhase;

  const handlePhaseClick = (phase: string) => {
    setTemporarySelectedPhase(phase);
    if (isEditMode) {
      setInternalSelectedPhase(phase);
      setIsModalOpen(true);

      return;
    }

    setTemporarySelectedPhase(undefined);
    // Update internal state if not controlled
    if (selectedPhase === undefined) {
      setInternalSelectedPhase(phase);
    }

    // Notify parent if callback provided
    if (onPhaseSelect) {
      onPhaseSelect(phase);
    }
  };

  const saveChanges = () => {
    console.log("Changes saved");
  };

  const changePhase = () => {
    // Update internal state if not controlled
    if (selectedPhase === undefined) {
      setInternalSelectedPhase(temporarySelectedPhase);
    }

    // Notify parent if callback provided
    if (onPhaseSelect && temporarySelectedPhase !== undefined) {
      onPhaseSelect(temporarySelectedPhase);
    }
  };

  const getDateColor = (date?: string) => {
    if (!date) return "text-default-500";
    const currentDate = new Date();
    const [day, month, year] = date.split("/").map(Number);
    const dateObj = new Date(year, month - 1, day);

    return dateObj < currentDate
      ? "font-bold text-red-500"
      : "text-default-500";
  };

  return (
    <div className="relative pt-4">
      <div className="absolute top-[3.4rem] left-0 ml-10 h-1 bg-default-500 w-[94%]" />
      <div className="relative flex justify-between">
        {timelineSteps.map((step) => {
          // Get colors for this phase
          const colors = extractPhaseColors(step.label);
          const isSelected = effectiveSelectedPhase === step.label;

          return (
            <button
              key={step.label}
              aria-label={`Select ${step.label} phase`}
              className="flex flex-col items-center cursor-pointer bg-transparent border-none appearance-none"
              type="button"
              onClick={() => handlePhaseClick(step.label)}
            >
              <p
                className={`text-sm mb-2 ${isSelected ? "font-bold" : ""} transition-all duration-200 hover:text-primary`}
              >
                {step.label}{" "}
                {Math.random() > 0.5 ? (
                  <svg
                    className="size-4 inline-block ml-1"
                    fill="none"
                    stroke="green"
                    strokeWidth={1.5}
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                ) : (
                  ""
                )}
              </p>
              <div className="relative">
                <div
                  className={`w-6 h-6 rounded-full border-2 z-10 bg-background flex items-center justify-center overflow-hidden ${getPhaseStyle(step.label, false)} transition-all duration-200 ${step.progress > 0 && step.progress < 100 ? "" : "hover:scale-110"}`}
                >
                  {step.progress > 0 && step.progress < 100 && (
                    // <div
                    //   className="hover:scale-110"
                    //   style={{
                    //     width: "100%",
                    //     height: "100%",
                    //     borderRadius: "50%",
                    //     border: `2px solid ${colors.border}`,
                    //     background: `conic-gradient(${colors.border} 0%, ${colors.border} ${step.progress}%, ${colors.bg} ${step.progress}%, ${colors.bg} 100%)`,
                    //     position: "absolute",
                    //     transition: "transform 0.3s ease",
                    //   }}
                    // />
                    <span
                      className="hover:scale-110"
                      style={{
                        width: "100%",
                        height: "100%",
                        borderRadius: "50%",
                        border: `2px solid ${colors.border}`,
                        background: `conic-gradient(${colors.border} 0%, ${colors.border} ${step.progress}%, ${colors.bg} ${step.progress}%, ${colors.bg} 100%)`,
                        position: "absolute",
                        transition: "transform 0.3s ease",
                      }}
                    >
                      ✓
                    </span>
                  )}
                  {step.progress === 100 && (
                    <span className="text-xs z-20">✓</span>
                  )}
                </div>
              </div>
              <p className="text-xs text-default-500 mt-1">{step.progress}%</p>
              <p className={`text-xs text-default-500 mt-1`}>
                <span>{step.dates[0]}</span>
                <br />{" "}
                <span className={getDateColor(step.dates[1])}>
                  {step.dates[1]}
                </span>
              </p>
            </button>
          );
        })}
      </div>

      <Modal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)}>
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1">
                ⚠️¡Cuidado!⚠️
              </ModalHeader>
              <ModalBody>
                <div className="flex flex-col gap-2">
                  <p className="text-sm text-default-500">
                    No has guardado los cambios. ¿Estás seguro de que deseas
                    continuar?
                  </p>
                </div>
              </ModalBody>
              <ModalFooter>
                <Button
                  color="default"
                  variant="light"
                  onPress={() => {
                    setIsModalOpen(false);
                    onClose();
                  }}
                >
                  Cancelar
                </Button>
                <Button
                  color="default"
                  variant="solid"
                  onPress={() => {
                    changePhase();
                    setIsModalOpen(false);
                    onClose();
                  }}
                >
                  Continuar sin guardar
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </div>
  );
};
